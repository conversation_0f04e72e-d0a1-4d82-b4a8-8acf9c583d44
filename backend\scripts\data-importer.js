#!/usr/bin/env node

/**
 * Comprehensive data importer for supermarket products
 * Supports multiple data sources: CSV, JSON, APIs, web scraping
 */

const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');

class DataImporter {
  constructor() {
    this.mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    this.dbName = 'smart_shopper_db';
    this.client = null;
    this.db = null;
  }

  async connect() {
    try {
      this.client = new MongoClient(this.mongoUri);
      await this.client.connect();
      this.db = this.client.db(this.dbName);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.log('⚠️  MongoDB not available, using file-based storage');
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
    }
  }

  async importFromCSV(csvFile, supermarketId) {
    console.log(`📥 Importing from CSV: ${csvFile}`);
    
    const csvContent = fs.readFileSync(csvFile, 'utf8');
    const products = this.parseCSV(csvContent, supermarketId);
    
    await this.saveProducts(products, supermarketId);
    return products;
  }

  async importFromJSON(jsonFile, supermarketId) {
    console.log(`📥 Importing from JSON: ${jsonFile}`);
    
    const jsonContent = fs.readFileSync(jsonFile, 'utf8');
    const data = JSON.parse(jsonContent);
    const products = Array.isArray(data) ? data : data.products || [];
    
    // Ensure products have correct supermarketId
    products.forEach(product => {
      product.supermarketId = supermarketId;
      product.lastUpdated = Date.now();
    });
    
    await this.saveProducts(products, supermarketId);
    return products;
  }

  parseCSV(csvContent, supermarketId) {
    const lines = csvContent.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    const products = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const product = { supermarketId, lastUpdated: Date.now() };
      
      headers.forEach((header, index) => {
        const value = values[index];
        
        switch (header) {
          case 'price':
            product[header] = parseFloat(value) || 0;
            break;
          case 'isAvailable':
            product[header] = value.toLowerCase() === 'true';
            break;
          default:
            product[header] = value || null;
        }
      });
      
      if (!product.id) {
        product.id = `${supermarketId}_${product.productId}_${Date.now().toString().slice(-6)}`;
      }
      
      products.push(product);
    }
    
    return products;
  }

  async saveProducts(products, supermarketId) {
    // Save to MongoDB if available
    if (this.db) {
      try {
        const collection = this.db.collection('supermarket_products');
        
        // Remove existing products for this supermarket
        await collection.deleteMany({ supermarketId });
        
        // Insert new products
        if (products.length > 0) {
          await collection.insertMany(products);
        }
        
        console.log(`✅ Saved ${products.length} products to MongoDB for ${supermarketId}`);
      } catch (error) {
        console.error('❌ MongoDB save failed:', error.message);
      }
    }
    
    // Always save to file as backup
    await this.saveToFile(products, supermarketId);
  }

  async saveToFile(products, supermarketId) {
    const outputPath = path.join(__dirname, '..', 'data', 'supermarkets', `${supermarketId}-products.js`);
    const constantName = `${supermarketId.toUpperCase()}_PRODUCTS`;
    
    const jsContent = `// ${supermarketId} product catalog - Updated ${new Date().toISOString()}
const ${constantName} = ${JSON.stringify(products, null, 2)};

module.exports = { ${constantName} };`;

    fs.writeFileSync(outputPath, jsContent);
    console.log(`✅ Saved ${products.length} products to ${outputPath}`);
  }

  async updatePrices(supermarketId, priceUpdates) {
    console.log(`💰 Updating prices for ${supermarketId}`);
    
    if (this.db) {
      const collection = this.db.collection('supermarket_products');
      
      for (const update of priceUpdates) {
        await collection.updateOne(
          { supermarketId, productId: update.productId },
          { 
            $set: { 
              price: update.price, 
              lastUpdated: Date.now(),
              isAvailable: update.isAvailable !== false
            } 
          }
        );
      }
    }
    
    // Update file as well
    const filePath = path.join(__dirname, '..', 'data', 'supermarkets', `${supermarketId}-products.js`);
    if (fs.existsSync(filePath)) {
      const { [supermarketId.toUpperCase() + '_PRODUCTS']: products } = require(filePath);
      
      priceUpdates.forEach(update => {
        const product = products.find(p => p.productId === update.productId);
        if (product) {
          product.price = update.price;
          product.lastUpdated = Date.now();
          product.isAvailable = update.isAvailable !== false;
        }
      });
      
      await this.saveToFile(products, supermarketId);
    }
  }

  async validateData(supermarketId) {
    console.log(`🔍 Validating data for ${supermarketId}`);
    
    const filePath = path.join(__dirname, '..', 'data', 'supermarkets', `${supermarketId}-products.js`);
    if (!fs.existsSync(filePath)) {
      console.log(`❌ No data file found for ${supermarketId}`);
      return false;
    }
    
    const { [supermarketId.toUpperCase() + '_PRODUCTS']: products } = require(filePath);
    
    const issues = [];
    products.forEach((product, index) => {
      if (!product.id) issues.push(`Product ${index}: Missing ID`);
      if (!product.productId) issues.push(`Product ${index}: Missing productId`);
      if (!product.name) issues.push(`Product ${index}: Missing name`);
      if (typeof product.price !== 'number') issues.push(`Product ${index}: Invalid price`);
    });
    
    if (issues.length > 0) {
      console.log(`❌ Validation issues for ${supermarketId}:`);
      issues.forEach(issue => console.log(`  - ${issue}`));
      return false;
    }
    
    console.log(`✅ Data validation passed for ${supermarketId} (${products.length} products)`);
    return true;
  }
}

// CLI interface
async function main() {
  const importer = new DataImporter();
  await importer.connect();
  
  const command = process.argv[2];
  const supermarketId = process.argv[3];
  const filePath = process.argv[4];
  
  try {
    switch (command) {
      case 'csv':
        if (!supermarketId || !filePath) {
          console.log('Usage: node data-importer.js csv <supermarketId> <csvFile>');
          process.exit(1);
        }
        await importer.importFromCSV(filePath, supermarketId);
        break;
        
      case 'json':
        if (!supermarketId || !filePath) {
          console.log('Usage: node data-importer.js json <supermarketId> <jsonFile>');
          process.exit(1);
        }
        await importer.importFromJSON(filePath, supermarketId);
        break;
        
      case 'validate':
        if (!supermarketId) {
          console.log('Usage: node data-importer.js validate <supermarketId>');
          process.exit(1);
        }
        await importer.validateData(supermarketId);
        break;
        
      default:
        console.log('Available commands:');
        console.log('  csv <supermarketId> <csvFile>     - Import from CSV');
        console.log('  json <supermarketId> <jsonFile>   - Import from JSON');
        console.log('  validate <supermarketId>          - Validate existing data');
        console.log('');
        console.log('Examples:');
        console.log('  node data-importer.js csv paknsave paknsave-products.csv');
        console.log('  node data-importer.js validate newworld');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await importer.disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = DataImporter;
