
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { ShoppingList, ShoppingListItem, SortOption, SelectedProductInfo, Collaborator } from '../types';
import { shoppingListService } from '../services/shoppingListService';
import ShoppingListItemRow from '../components/list/ShoppingListItemRow';
import AddItemForm from '../components/list/AddItemForm';
import SortControls from '../components/list/SortControls';
import BrandSelectionModal from '../components/list/BrandSelectionModal';
import ShareListModal from '../components/list/ShareListModal'; // Import ShareListModal
import ShoppingListTotalComparison from '../components/list/ShoppingListTotalComparison';
import { LIST_THEME_COLORS, ShareIcon } from '../constants'; // Import ShareIcon
import But<PERSON> from '../components/ui/Button'; // For the share button

const ListPage: React.FC = () => {
  const { listId } = useParams<{ listId: string }>();
  const navigate = useNavigate();
  const [list, setList] = useState<ShoppingList | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState<SortOption>(SortOption.DATE_ADDED_NEWEST);

  // State for brand selection modal
  const [itemPendingBrandSelection, setItemPendingBrandSelection] = useState<ShoppingListItem | null>(null);
  const [availableBrandsForItem, setAvailableBrandsForItem] = useState<string[]>([]);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);

  // State for Share Modal
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  const fetchListDetails = useCallback(async () => {
    if (!listId) {
      setError("No list ID provided.");
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const fetchedList = await shoppingListService.getListById(listId);
      if (fetchedList) {
        setList(fetchedList);
      } else {
        setError(`List with ID ${listId} not found.`);
      }
    } catch (err) {
      console.error("Failed to fetch list details:", err);
      setError("Failed to load list. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [listId]); 

  useEffect(() => {
    fetchListDetails();
  }, [fetchListDetails]);

  const handleAddItem = async (
    itemData: Omit<ShoppingListItem, 'id' | 'addedAt' | 'isChecked' | 'brand'>,
    selectedProductInfo?: SelectedProductInfo
  ): Promise<ShoppingListItem | null> => {
    if (!listId) return null;
    try {
      const addedItem = await shoppingListService.addItemToList(listId, itemData);
      fetchListDetails(); 

      if (addedItem && selectedProductInfo?.brands && selectedProductInfo.brands.length > 0) {
        setItemPendingBrandSelection(addedItem); 
        setAvailableBrandsForItem(selectedProductInfo.brands);
        setIsBrandModalOpen(true);
      }
      return addedItem;
    } catch (err) {
      console.error("Failed to add item:", err);
      throw err; 
    }
  };

  const handleSaveBrand = async (itemId: string, brand: string) => {
    if (!listId || !itemId) return;
    try {
      await shoppingListService.updateItemInList(listId, itemId, { brand });
      fetchListDetails(); 
    } catch (err) {
      console.error("Failed to save brand:", err);
      alert("Could not update brand. Please try again.");
    } finally {
      setIsBrandModalOpen(false);
      setItemPendingBrandSelection(null);
      setAvailableBrandsForItem([]);
    }
  };

  const handleCloseBrandModal = () => {
    setIsBrandModalOpen(false);
    setItemPendingBrandSelection(null);
    setAvailableBrandsForItem([]);
  };


  const handleToggleChecked = async (itemId: string) => {
    if (!listId) return;
    try {
      await shoppingListService.toggleItemChecked(listId, itemId);
      fetchListDetails();
    } catch (err) {
      console.error("Failed to toggle item:", err);
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    if (!listId) return;
    if (window.confirm("Are you sure you want to remove this item?")) {
      try {
        await shoppingListService.removeItemFromList(listId, itemId);
        fetchListDetails();
      } catch (err) {
        console.error("Failed to remove item:", err);
      }
    }
  };
  
  const handleEditItem = (item: ShoppingListItem) => {
    console.log("Editing item:", item);
    alert(`Editing item "${item.name}" (ID: ${item.id}) - Full edit UI coming soon!`);
  };

  const handleOpenShareModal = () => {
    if (list) {
      setIsShareModalOpen(true);
    }
  };

  const handleCollaboratorAdded = () => {
    fetchListDetails(); // Re-fetch list details to show new collaborator
  };

  const sortedItems = useMemo(() => {
    if (!list?.items) return [];
    const itemsCopy = [...list.items];
    switch (sortOption) {
      case SortOption.NAME_ASC:
        return itemsCopy.sort((a, b) => a.name.localeCompare(b.name));
      case SortOption.NAME_DESC:
        return itemsCopy.sort((a, b) => b.name.localeCompare(a.name));
      case SortOption.DATE_ADDED_OLDEST:
        return itemsCopy.sort((a, b) => a.addedAt - b.addedAt);
      case SortOption.DATE_ADDED_NEWEST:
        return itemsCopy.sort((a, b) => b.addedAt - a.addedAt);
      case SortOption.CHECKED_FIRST:
        return itemsCopy.sort((a, b) => (b.isChecked ? 1 : 0) - (a.isChecked ? 1 : 0) || b.addedAt - a.addedAt);
      case SortOption.UNCHECKED_FIRST:
        return itemsCopy.sort((a, b) => (a.isChecked ? 1 : 0) - (b.isChecked ? 1 : 0) || b.addedAt - a.addedAt);
      default:
        return itemsCopy;
    }
  }, [list?.items, sortOption]);

  const totalListPrice = useMemo(() => {
    return sortedItems.reduce((total, item) => {
      if (item.pricePerUnit && item.quantity && !item.isChecked) { 
        return total + (item.pricePerUnit * item.quantity);
      }
      return total;
    }, 0);
  }, [sortedItems]);

  if (isLoading && !list) { 
    return (
      <div className="flex justify-center items-center h-64">
        <svg className="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10 text-red-500">
        <p className="text-xl font-semibold">{error}</p>
        <Link to="/lists" className="text-primary hover:underline mt-4 inline-block">Go back to lists</Link>
      </div>
    );
  }

  if (!list) {
    return (
      <div className="text-center py-10">
        <p className="text-xl text-gray-600 dark:text-gray-400">List not found.</p>
        <Link to="/lists" className="text-primary hover:underline mt-4 inline-block">Go back to lists</Link>
      </div>
    );
  }

  const themeBgClass = LIST_THEME_COLORS[list.themeColor] || 'bg-primary';

  return (
    <div className="max-w-3xl mx-auto">
      <div className={`p-6 rounded-t-lg shadow-md ${themeBgClass} text-white`}>
        <div className="flex justify-between items-start mb-2">
            <Link to="/lists" className="text-sm opacity-80 hover:opacity-100 inline-block">&larr; Back to Lists</Link>
            <Button 
                onClick={handleOpenShareModal} 
                variant="ghost" 
                size="sm"
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                leftIcon={<ShareIcon className="h-4 w-4"/>}
            >
                Share
            </Button>
        </div>
        <div className="flex justify-between items-center">
          <h1 className="text-4xl font-bold">{list.name}</h1>
          {list.icon && <list.icon className="w-10 h-10 opacity-50" />}
        </div>
        <div className="flex items-center mt-2">
            <p className="text-sm opacity-90 mr-4">{list.items.length} item(s)</p>
            <div className="flex -space-x-1">
              {list.collaborators.slice(0, 4).map((collab: Collaborator) => (
                <img 
                  key={collab.id} 
                  className="inline-block h-6 w-6 rounded-full ring-1 ring-white/50" 
                  src={collab.avatarUrl} 
                  alt={collab.name}
                  title={collab.name}
                />
              ))}
              {list.collaborators.length > 4 && (
                <span className="flex items-center justify-center h-6 w-6 rounded-full bg-white/30 text-xs font-medium text-white ring-1 ring-white/50">
                  +{list.collaborators.length - 4}
                </span>
              )}
            </div>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-dark shadow-md rounded-b-lg overflow-hidden">
        <AddItemForm onAddItem={handleAddItem} listId={list.id} />
        
        <div className="px-4 pt-4">
           <SortControls currentSort={sortOption} onSortChange={setSortOption} />
        </div>

        {isLoading && sortedItems.length === 0 && ( 
             <div className="flex justify-center items-center py-10"><span>Loading items...</span></div>
        )}

        {!isLoading && sortedItems.length === 0 ? (
          <p className="p-6 text-center text-gray-500 dark:text-gray-400">This list is empty. Add some items above!</p>
        ) : (
          <div>
            {sortedItems.map((item) => (
              <ShoppingListItemRow
                key={item.id}
                item={item}
                onToggleChecked={handleToggleChecked}
                onRemoveItem={handleRemoveItem}
                onEditItem={handleEditItem}
              />
            ))}
          </div>
        )}

        {sortedItems.length > 0 && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-neutral-darker text-right">
            <span className="text-lg font-semibold text-gray-700 dark:text-gray-200">
              Estimated Total (Pending): ${totalListPrice.toFixed(2)}
            </span>
          </div>
        )}
      </div>

      {/* Shopping List Total Comparison */}
      <ShoppingListTotalComparison items={sortedItems.filter(item => !item.isChecked)} />
      <BrandSelectionModal
        isOpen={isBrandModalOpen}
        onClose={handleCloseBrandModal}
        item={itemPendingBrandSelection}
        availableBrands={availableBrandsForItem}
        onSaveBrand={handleSaveBrand}
      />
      {list && (
        <ShareListModal
            isOpen={isShareModalOpen}
            onClose={() => setIsShareModalOpen(false)}
            listId={list.id}
            listName={list.name}
            onCollaboratorAdded={handleCollaboratorAdded}
        />
      )}
    </div>
  );
};

export default ListPage;
