/**
 * Denormalized Schema - Optimized for read speed
 * Trade-off: Faster reads, more storage space, complex updates
 */

// Single collection with all price data embedded
const ProductPriceSchema = {
  _id: "ObjectId",
  id: "prod_milk_1l",
  name: "Milk",
  category: "dairy",
  icon: "milk",
  image: "milk.jpg",
  
  // All prices embedded for fast single-query access
  prices: {
    paknsave: {
      price: 2.89,
      unit: "each",
      size: "1L",
      available: true,
      url: "/shop/product/anchor-blue-milk-1l",
      updated: "2024-01-01T00:00:00Z"
    },
    newworld: {
      price: 3.19,
      unit: "each", 
      size: "1L",
      available: true,
      url: "/shop/product/anchor-blue-milk-1l",
      updated: "2024-01-01T00:00:00Z"
    },
    woolworths: {
      price: 3.09,
      unit: "each",
      size: "1L", 
      available: true,
      url: "/shop/product/anchor-blue-milk-1l",
      updated: "2024-01-01T00:00:00Z"
    },
    warehouse: {
      price: 3.29,
      unit: "each",
      size: "1L",
      available: true,
      url: "/p/anchor-blue-milk-1l",
      updated: "2024-01-01T00:00:00Z"
    }
  },
  
  // Pre-calculated for instant access
  lowestPrice: {
    supermarket: "paknsave",
    price: 2.89
  },
  
  priceRange: {
    min: 2.89,
    max: 3.29,
    savings: 0.40
  },
  
  lastUpdated: "2024-01-01T00:00:00Z"
};

// Supermarkets as a simple lookup object (could be cached in memory)
const SupermarketLookup = {
  paknsave: { name: "Pak'nSave", color: "#FFD700", logo: "paknsave.png" },
  newworld: { name: "New World", color: "#E31E24", logo: "newworld.png" },
  woolworths: { name: "Woolworths", color: "#00A651", logo: "woolworths.png" },
  warehouse: { name: "The Warehouse", color: "#D50000", logo: "warehouse.png" }
};

module.exports = {
  ProductPriceSchema,
  SupermarketLookup
};
