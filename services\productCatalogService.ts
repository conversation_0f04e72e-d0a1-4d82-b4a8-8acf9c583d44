
import { Product } from '../types';

const API_BASE_URL = '/api';

export const productCatalogService = {
  // GET /api/products - Fetch all products from backend
  getAllProducts: async (): Promise<Product[]> => {
    console.log("API call: GET /api/products");
    const response = await fetch(`${API_BASE_URL}/products`);
    if (!response.ok) {
      throw new Error('Failed to fetch products');
    }
    return response.json();
  },

  // GET /api/products/search?q=searchTerm - Search products by name
  findProductsByName: async (searchTerm: string): Promise<Product[]> => {
    console.log(`API call: GET /api/products/search?q=${searchTerm}`);
    const trimmedSearchTerm = searchTerm.trim();
    if (!trimmedSearchTerm) {
      return [];
    }

    const response = await fetch(`${API_BASE_URL}/products/search?q=${encodeURIComponent(trimmedSearchTerm)}`);
    if (!response.ok) {
      throw new Error('Failed to search products');
    }
    return response.json();
  },

  // GET /api/products/:id - Fetch a product by ID
  getProductById: async (id: string): Promise<Product | undefined> => {
    console.log(`API call: GET /api/products/${id}`);
    const response = await fetch(`${API_BASE_URL}/products/${id}`);
    if (!response.ok) {
      if (response.status === 404) return undefined;
      throw new Error('Failed to fetch product');
    }
    return response.json();
  }
};