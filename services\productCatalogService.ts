
import { Product } from '../types';

const MOCK_PRODUCTS: Product[] = [
  { id: 'prod_1', name: 'Eggs', iconName: 'EggIcon', brands: ['Farm Fresh', 'Organic Valley', 'Happy Eggs'], defaultUnit: 'dozen', category: 'Dairy & Alternatives', estimatedPrice: 3.50, imageUrl: 'https://picsum.photos/seed/eggs_product/200/200' },
  { id: 'prod_2', name: 'Milk', iconName: 'MilkIcon', brands: ['Horizon Organic', 'Fairlife', 'Store Brand Whole'], defaultUnit: 'gallon', category: 'Dairy & Alternatives', estimatedPrice: 4.00, imageUrl: 'https://picsum.photos/seed/milk_product/200/200' },
  { id: 'prod_3', name: 'Coffee Beans', iconName: 'CoffeeIcon', brands: ['Starbucks Pike Place', 'Peet\'s <PERSON>on\'s', 'Local Roasters Choice'], defaultUnit: 'lb', category: 'Pantry', estimatedPrice: 12.99, imageUrl: 'https://picsum.photos/seed/coffee_product/200/200' },
  { id: 'prod_4', name: 'Sour<PERSON>ugh Bread', iconName: 'BreadIcon', brands: ['Artisan Baker', 'Bakery Local'], defaultUnit: 'loaf', category: 'Bakery', estimatedPrice: 5.50, imageUrl: 'https://picsum.photos/seed/bread_product/200/200' },
  { id: 'prod_5', name: 'Apples', iconName: 'AppleIcon', defaultUnit: 'lb', category: 'Produce', estimatedPrice: 1.99, imageUrl: 'https://picsum.photos/seed/apples_product/200/200' },
  { id: 'prod_6', name: 'Chicken Breast', iconName: 'TagIcon', brands: ['Tyson', 'Perdue', 'Organic Free-Range'], defaultUnit: 'lb', category: 'Meat & Seafood', estimatedPrice: 6.99, imageUrl: 'https://picsum.photos/seed/chicken_product/200/200' },
  { id: 'prod_7', name: 'Pasta', iconName: 'TagIcon', brands: ['Barilla', 'De Cecco'], defaultUnit: 'box', category: 'Pantry', estimatedPrice: 2.50, imageUrl: 'https://picsum.photos/seed/pasta_product/200/200' },
  { id: 'prod_8', name: 'Olive Oil', iconName: 'TagIcon', brands: ['California Olive Ranch', 'Bertolli'], defaultUnit: 'bottle', category: 'Pantry', estimatedPrice: 10.00, imageUrl: 'https://picsum.photos/seed/oil_product/200/200' },
];

// Simulates fetching all products from a backend API
// In a real app, this would be:
// const response = await fetch('/api/products');
// const data = await response.json();
// return data;
export const productCatalogService = {
  getAllProducts: async (): Promise<Product[]> => {
    console.log("Simulating API call: GET /api/products");
    return new Promise((resolve) => setTimeout(() => resolve([...MOCK_PRODUCTS]), 200));
  },

  // Simulates finding products by name from a backend API
  // In a real app, this would be:
  // const response = await fetch(`/api/products/search?q=${encodeURIComponent(searchTerm)}`);
  // const data = await response.json();
  // return data;
  findProductsByName: async (searchTerm: string): Promise<Product[]> => {
    console.log(`Simulating API call: GET /api/products/search?q=${searchTerm}`);
    return new Promise((resolve) => {
      const trimmedSearchTerm = searchTerm.trim();
      if (!trimmedSearchTerm) {
        resolve([]);
        return;
      }
      const lowerSearchTerm = trimmedSearchTerm.toLowerCase();
      const results = MOCK_PRODUCTS.filter(p => 
        p.name.trim().toLowerCase().includes(lowerSearchTerm)
      );
      setTimeout(() => resolve(results), 100);
    });
  },

  // Simulates fetching a product by ID from a backend API
  // In a real app, this would be:
  // const response = await fetch(`/api/products/${id}`);
  // const data = await response.json();
  // return data;
  getProductById: async (id: string): Promise<Product | undefined> => {
    console.log(`Simulating API call: GET /api/products/${id}`);
    return new Promise((resolve) => {
      const product = MOCK_PRODUCTS.find(p => p.id === id);
      setTimeout(() => resolve(product), 50);
    });
  }
};