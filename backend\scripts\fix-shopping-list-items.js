#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix shopping list items that were added as text-only
 * and link them to products in the database for price comparison
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

// Mapping of common item names to product IDs
const ITEM_NAME_TO_PRODUCT_ID = {
  'milk': 'prod_milk_1l',
  'eggs': 'prod_eggs_dozen', 
  'coffee': null, // No coffee product in database yet
  'bread': 'prod_bread_white',
  'white bread': 'prod_bread_white',
  'bananas': 'prod_bananas',
  'chicken': 'prod_chicken_breast',
  'chicken breast': 'prod_chicken_breast',
  'rice': 'prod_rice_1kg',
  'pasta': 'prod_pasta_500g',
  'tomatoes': 'prod_tomatoes',
  'cheese': 'prod_cheese_tasty',
  'tasty cheese': 'prod_cheese_tasty',
  'olive oil': 'prod_olive_oil'
};

async function fixShoppingListItems() {
  const client = new MongoClient(process.env.MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db('smart_shopper_db');
    const shoppingListsCollection = db.collection('shopping_lists');
    const productsCollection = db.collection('products');
    
    // Get all products for reference
    const products = await productsCollection.find({}).toArray();
    const productMap = {};
    products.forEach(product => {
      productMap[product.id] = product;
    });
    
    console.log('🔍 Finding shopping lists with items to fix...');
    
    // Find all shopping lists
    const shoppingLists = await shoppingListsCollection.find({}).toArray();
    
    let totalItemsFixed = 0;
    
    for (const list of shoppingLists) {
      console.log(`\n📋 Checking list: "${list.name}" (${list.items.length} items)`);
      
      let listUpdated = false;
      
      for (let i = 0; i < list.items.length; i++) {
        const item = list.items[i];
        
        // Skip items that already have productId
        if (item.productId) {
          console.log(`  ✅ ${item.name} - already linked to product`);
          continue;
        }
        
        // Try to match item name to product
        const itemNameLower = item.name.toLowerCase().trim();
        const productId = ITEM_NAME_TO_PRODUCT_ID[itemNameLower];
        
        if (productId && productMap[productId]) {
          const product = productMap[productId];
          
          // Update the item with product information
          list.items[i] = {
            ...item,
            productId: product.id,
            unit: product.defaultUnit,
            pricePerUnit: product.estimatedPrice
          };
          
          console.log(`  🔧 Fixed "${item.name}" -> linked to "${product.name}" (${product.id})`);
          listUpdated = true;
          totalItemsFixed++;
        } else {
          console.log(`  ⚠️  "${item.name}" - no matching product found`);
        }
      }
      
      // Update the shopping list if any items were fixed
      if (listUpdated) {
        await shoppingListsCollection.updateOne(
          { _id: list._id },
          { $set: { items: list.items } }
        );
        console.log(`  💾 Updated shopping list "${list.name}"`);
      }
    }
    
    console.log(`\n🎉 Fixed ${totalItemsFixed} items across all shopping lists!`);
    
    // Show summary
    console.log('\n📊 Summary of available products:');
    Object.entries(ITEM_NAME_TO_PRODUCT_ID).forEach(([name, productId]) => {
      if (productId && productMap[productId]) {
        console.log(`  ✅ "${name}" -> ${productMap[productId].name}`);
      } else {
        console.log(`  ❌ "${name}" -> No product available`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error fixing shopping list items:', error);
  } finally {
    await client.close();
  }
}

if (require.main === module) {
  fixShoppingListItems();
}

module.exports = fixShoppingListItems;
