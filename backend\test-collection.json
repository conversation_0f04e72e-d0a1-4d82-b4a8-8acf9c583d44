{"info": {"name": "Smart Shopper API Tests", "description": "Complete test collection for Smart Shopper backend API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5000/api/health", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "health"]}}}, {"name": "Get All Supermarkets", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5000/api/supermarkets", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "supermarkets"]}}}, {"name": "Get Pak'nSave Products", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5000/api/supermarkets/paknsave/products", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "supermarkets", "paknsave", "products"]}}}, {"name": "Compare Milk Prices", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5000/api/products/prod_milk_1l/prices", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "products", "prod_milk_1l", "prices"]}}}, {"name": "Create Shopping List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Weekly Groceries\",\n  \"themeColor\": \"blue\",\n  \"iconName\": \"ShoppingCartIcon\"\n}"}, "url": {"raw": "http://localhost:5000/api/shopping-lists", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "shopping-lists"]}}}, {"name": "Add Item to List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Milk\",\n  \"productId\": \"prod_milk_1l\",\n  \"quantity\": 2,\n  \"unit\": \"each\"\n}"}, "url": {"raw": "http://localhost:5000/api/shopping-lists/{{listId}}/items", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "shopping-lists", "{{listId}}", "items"]}}}, {"name": "Get Shopping List Price Comparison", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "http://localhost:5000/api/shopping-lists/{{listId}}/price-comparison", "protocol": "http", "host": ["localhost"], "port": "5000", "path": ["api", "shopping-lists", "{{listId}}", "price-comparison"]}}}]}