#!/usr/bin/env node

/**
 * Test script to compare API response sizes and performance
 * Shows the benefits of the optimized v2 endpoints
 */

const https = require('https');
const http = require('http');

async function fetchData(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    const start = Date.now();
    client.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        const end = Date.now();
        resolve({
          data: JSON.parse(data),
          size: Buffer.byteLength(data, 'utf8'),
          time: end - start
        });
      });
    }).on('error', reject);
  });
}

async function runComparison() {
  console.log('🧪 API Optimization Comparison Test\n');
  
  const baseUrl = 'http://localhost:5000/api';
  
  try {
    // Test 1: Supermarkets endpoint
    console.log('📊 Test 1: Supermarkets Endpoint');
    console.log('=' .repeat(50));
    
    const v1Supermarkets = await fetchData(`${baseUrl}/supermarkets`);
    const v2Supermarkets = await fetchData(`${baseUrl}/v2/supermarkets`);
    
    console.log(`V1 (Original):`);
    console.log(`  Response size: ${v1Supermarkets.size} bytes`);
    console.log(`  Response time: ${v1Supermarkets.time}ms`);
    console.log(`  Fields per item: ${Object.keys(v1Supermarkets.data[0] || {}).length}`);
    
    console.log(`\nV2 (Optimized):`);
    console.log(`  Response size: ${v2Supermarkets.size} bytes`);
    console.log(`  Response time: ${v2Supermarkets.time}ms`);
    console.log(`  Fields per item: ${Object.keys(v2Supermarkets.data[0] || {}).length}`);
    
    const sizeSavings = ((v1Supermarkets.size - v2Supermarkets.size) / v1Supermarkets.size * 100).toFixed(1);
    const timeSavings = v1Supermarkets.time - v2Supermarkets.time;
    
    console.log(`\n✅ Optimization Results:`);
    console.log(`  Size reduction: ${sizeSavings}% (${v1Supermarkets.size - v2Supermarkets.size} bytes saved)`);
    console.log(`  Time difference: ${timeSavings}ms`);
    
    // Test 2: Price comparison endpoint
    console.log('\n\n💰 Test 2: Price Comparison Endpoint');
    console.log('=' .repeat(50));
    
    const v1Prices = await fetchData(`${baseUrl}/products/prod_milk_1l/prices`);
    const v2Prices = await fetchData(`${baseUrl}/v2/products/prod_milk_1l/prices`);
    
    console.log(`V1 (Original):`);
    console.log(`  Response size: ${v1Prices.size} bytes`);
    console.log(`  Response time: ${v1Prices.time}ms`);
    console.log(`  Query method: Multiple separate queries`);
    
    console.log(`\nV2 (Optimized):`);
    console.log(`  Response size: ${v2Prices.size} bytes`);
    console.log(`  Response time: ${v2Prices.time}ms`);
    console.log(`  Query method: Single aggregated query`);
    
    const priceSizeSavings = ((v1Prices.size - v2Prices.size) / v1Prices.size * 100).toFixed(1);
    const priceTimeSavings = v1Prices.time - v2Prices.time;
    
    console.log(`\n✅ Optimization Results:`);
    console.log(`  Size difference: ${priceSizeSavings}% (${v1Prices.size - v2Prices.size} bytes)`);
    console.log(`  Time difference: ${priceTimeSavings}ms`);
    
    // Summary
    console.log('\n\n🎯 Overall Optimization Summary');
    console.log('=' .repeat(50));
    console.log(`📊 Supermarkets endpoint: ${sizeSavings}% smaller`);
    console.log(`💰 Price comparison: ${Math.abs(priceSizeSavings)}% size difference`);
    console.log(`⚡ Average time improvement: ${((timeSavings + priceTimeSavings) / 2).toFixed(1)}ms`);
    
    console.log('\n🚀 Benefits of V2 API:');
    console.log('  ✅ Smaller response payloads');
    console.log('  ✅ Faster database queries (aggregation)');
    console.log('  ✅ Reduced bandwidth usage');
    console.log('  ✅ Better mobile performance');
    console.log('  ✅ Lower server costs');
    
    console.log('\n📱 Frontend Integration:');
    console.log('  // Use optimized endpoints for better performance');
    console.log('  const supermarkets = await fetch("/api/v2/supermarkets");');
    console.log('  const prices = await fetch("/api/v2/products/prod_milk_1l/prices");');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure the server is running on http://localhost:5000');
  }
}

if (require.main === module) {
  runComparison();
}

module.exports = runComparison;
