import { Supermarket, SupermarketProduct, PriceComparison, ShoppingListPriceComparison } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

export const supermarketService = {
  // GET /api/supermarkets
  getAllSupermarkets: async (): Promise<Supermarket[]> => {
    const response = await fetch(`${API_BASE_URL}/supermarkets`);
    if (!response.ok) {
      throw new Error('Failed to fetch supermarkets');
    }
    return response.json();
  },

  // GET /api/supermarkets/:id/products
  getSupermarketProducts: async (supermarketId: string): Promise<SupermarketProduct[]> => {
    const response = await fetch(`${API_BASE_URL}/supermarkets/${supermarketId}/products`);
    if (!response.ok) {
      throw new Error(`Failed to fetch products for ${supermarketId}`);
    }
    return response.json();
  },

  // GET /api/products/:id/prices
  getProductPriceComparison: async (productId: string): Promise<PriceComparison> => {
    const response = await fetch(`${API_BASE_URL}/products/${productId}/prices`);
    if (!response.ok) {
      throw new Error('Failed to fetch price comparison');
    }
    return response.json();
  },

  // POST /api/shopping-lists/:id/price-comparison
  getShoppingListPriceComparison: async (listId: string): Promise<ShoppingListPriceComparison> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}/price-comparison`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    if (!response.ok) {
      throw new Error('Failed to get shopping list price comparison');
    }
    return response.json();
  },

  // Helper function to find cheapest supermarket for a product
  getCheapestOption: (priceComparison: PriceComparison) => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    if (availablePrices.length === 0) return null;
    
    return availablePrices.reduce((cheapest, current) => 
      current.price < cheapest.price ? current : cheapest
    );
  },

  // Helper function to calculate potential savings
  calculateSavings: (priceComparison: PriceComparison, quantity: number = 1) => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    if (availablePrices.length < 2) return 0;
    
    const prices = availablePrices.map(p => p.price * quantity);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    return maxPrice - minPrice;
  },

  // Helper function to format price with currency
  formatPrice: (price: number): string => {
    return `$${price.toFixed(2)}`;
  },

  // Helper function to get supermarket color
  getSupermarketColor: (supermarkets: Supermarket[], supermarketId: string): string => {
    const supermarket = supermarkets.find(s => s.id === supermarketId);
    return supermarket?.color || '#666666';
  }
};
