#!/usr/bin/env node

/**
 * Comprehensive API test script for Smart Shopper Backend
 * Tests all endpoints including MongoDB integration and price comparison
 * Run with: node test-api.js
 */

const API_BASE = 'http://localhost:5000/api';

async function testEndpoint(method, url, data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${API_BASE}${url}`, options);
    const result = await response.json();

    console.log(`✅ ${method} ${url} - Status: ${response.status}`);
    if (response.status >= 400) {
      console.log(`   ❌ Error: ${result.error || result.message}`);
      return { success: false, data: result, status: response.status };
    } else {
      const preview = JSON.stringify(result).substring(0, 150);
      console.log(`   📄 Response: ${preview}${preview.length >= 150 ? '...' : ''}`);
      return { success: true, data: result, status: response.status };
    }
  } catch (error) {
    console.log(`❌ ${method} ${url} - Network Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

function formatPrice(price) {
  return `$${price.toFixed(2)}`;
}

function displayPriceComparison(comparison) {
  console.log(`\n💰 Price Comparison for ${comparison.productName}:`);
  comparison.prices.forEach(price => {
    const status = price.isAvailable ? '✅' : '❌';
    const lowest = comparison.lowestPrice?.supermarketId === price.supermarketId ? ' 🏆 CHEAPEST' : '';
    console.log(`   ${status} ${price.supermarketName}: ${formatPrice(price.price)} (${price.size})${lowest}`);
  });

  if (comparison.lowestPrice) {
    console.log(`   💡 Best deal: ${comparison.lowestPrice.supermarketId} at ${formatPrice(comparison.lowestPrice.price)}`);
  }
}

async function runTests() {
  console.log('🧪 Testing Smart Shopper Backend API with MongoDB Integration\n');

  // Test health check
  console.log('🏥 Health Check:');
  const healthResult = await testEndpoint('GET', '/health');

  if (healthResult.success && healthResult.data.database === 'Connected') {
    console.log('   ✅ MongoDB is connected!');
  } else {
    console.log('   ⚠️  MongoDB not connected - some tests may fail');
  }

  // Test supermarkets
  console.log('\n🏪 Testing Supermarkets API:');
  const supermarketsResult = await testEndpoint('GET', '/supermarkets');
  if (supermarketsResult.success) {
    console.log(`   📊 Found ${supermarketsResult.data.length} supermarkets`);
    supermarketsResult.data.forEach(sm => {
      console.log(`      • ${sm.name} (${sm.id})`);
    });
  }

  // Test products
  console.log('\n📦 Testing Products API:');
  await testEndpoint('GET', '/products');
  await testEndpoint('GET', '/products/search?q=milk');

  // Test price comparison
  console.log('\n💰 Testing Price Comparison:');
  const priceResult = await testEndpoint('GET', '/products/prod_milk_1l/prices');
  if (priceResult.success) {
    displayPriceComparison(priceResult.data);
  }

  // Test supermarket-specific products
  console.log('\n🛒 Testing Supermarket Products:');
  const paknsaveResult = await testEndpoint('GET', '/supermarkets/paknsave/products');
  if (paknsaveResult.success) {
    console.log(`   📊 Pak'nSave has ${paknsaveResult.data.length} products`);
  }

  // Test shopping lists
  console.log('\n📝 Testing Shopping Lists API:');
  await testEndpoint('GET', '/shopping-lists');

  // Create a test shopping list
  const createResult = await testEndpoint('POST', '/shopping-lists', {
    name: 'Price Comparison Test',
    themeColor: 'green',
    iconName: 'ShoppingCartIcon'
  });

  if (createResult.success) {
    const listId = createResult.data.id;
    console.log(`\n📋 Created test list: ${listId}`);

    // Add items with product IDs for price comparison
    console.log('\n🛍️ Adding items to test price comparison:');

    const items = [
      { name: 'Milk', productId: 'prod_milk_1l', quantity: 2 },
      { name: 'Bread', productId: 'prod_bread_white', quantity: 1 },
      { name: 'Eggs', productId: 'prod_eggs_dozen', quantity: 1 }
    ];

    for (const item of items) {
      const itemResult = await testEndpoint('POST', `/shopping-lists/${listId}/items`, item);
      if (itemResult.success) {
        console.log(`   ✅ Added ${item.name} (qty: ${item.quantity})`);
      }
    }

    // Test price comparison for the entire list
    console.log('\n🏆 Testing Shopping List Price Comparison:');
    const comparisonResult = await testEndpoint('POST', `/shopping-lists/${listId}/price-comparison`);

    if (comparisonResult.success) {
      const comparison = comparisonResult.data;
      console.log('\n📊 Total Cost Comparison:');

      comparison.totalComparison.forEach(total => {
        const available = total.availableItems;
        const unavailable = total.unavailableItems;
        console.log(`   ${total.supermarketName}: ${formatPrice(total.totalCost)} (${available} available, ${unavailable} unavailable)`);
      });

      if (comparison.bestOverallOption) {
        const best = comparison.bestOverallOption;
        console.log(`\n🏆 Best Overall Deal: ${best.supermarketId}`);
        console.log(`   💰 Total Cost: ${formatPrice(best.totalCost)}`);
        console.log(`   💵 Potential Savings: ${formatPrice(best.savings)}`);
      }
    }

    // Test other list operations
    console.log('\n🔧 Testing List Operations:');
    await testEndpoint('GET', `/shopping-lists/${listId}`);

    // Add collaborator
    await testEndpoint('POST', `/shopping-lists/${listId}/collaborators`, {
      name: 'Test Collaborator'
    });

    // Clean up
    console.log('\n🧹 Cleaning up test data:');
    await testEndpoint('DELETE', `/shopping-lists/${listId}`);
  }

  console.log('\n✨ Comprehensive API testing completed!');
  console.log('\n📈 Test Summary:');
  console.log('   ✅ Health check and MongoDB connection');
  console.log('   ✅ Supermarket data retrieval');
  console.log('   ✅ Product catalog and search');
  console.log('   ✅ Price comparison across supermarkets');
  console.log('   ✅ Shopping list creation and management');
  console.log('   ✅ Shopping list total cost comparison');
  console.log('   ✅ Data cleanup');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  console.log('   Or install node-fetch: npm install node-fetch');
  process.exit(1);
}

runTests().catch(console.error);
