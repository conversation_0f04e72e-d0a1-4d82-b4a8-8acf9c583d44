#!/usr/bin/env node

/**
 * Simple API test script for Smart Shopper Backend
 * Run with: node test-api.js
 */

const API_BASE = 'http://localhost:5000/api';

async function testEndpoint(method, url, data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${API_BASE}${url}`, options);
    const result = await response.json();
    
    console.log(`✅ ${method} ${url} - Status: ${response.status}`);
    if (response.status >= 400) {
      console.log(`   Error: ${result.error || result.message}`);
    } else {
      console.log(`   Response: ${JSON.stringify(result).substring(0, 100)}...`);
    }
    
    return { success: response.ok, data: result, status: response.status };
  } catch (error) {
    console.log(`❌ ${method} ${url} - Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🧪 Testing Smart Shopper Backend API\n');
  
  // Test health check
  await testEndpoint('GET', '/health');
  
  // Test products
  console.log('\n📦 Testing Products API:');
  await testEndpoint('GET', '/products');
  await testEndpoint('GET', '/products/search?q=milk');
  await testEndpoint('GET', '/products/prod_1');
  
  // Test shopping lists
  console.log('\n📝 Testing Shopping Lists API:');
  await testEndpoint('GET', '/shopping-lists');
  
  // Try to create a list (will fail without database)
  const createResult = await testEndpoint('POST', '/shopping-lists', {
    name: 'Test List',
    themeColor: 'blue'
  });
  
  if (createResult.success) {
    const listId = createResult.data.id;
    console.log(`\n📋 Testing with created list ID: ${listId}`);
    
    // Test list operations
    await testEndpoint('GET', `/shopping-lists/${listId}`);
    
    // Test adding items
    const itemResult = await testEndpoint('POST', `/shopping-lists/${listId}/items`, {
      name: 'Test Item',
      quantity: 2
    });
    
    if (itemResult.success) {
      const itemId = itemResult.data.id;
      await testEndpoint('PUT', `/shopping-lists/${listId}/items/${itemId}`, {
        quantity: 3
      });
      await testEndpoint('PATCH', `/shopping-lists/${listId}/items/${itemId}/toggle`);
      await testEndpoint('DELETE', `/shopping-lists/${listId}/items/${itemId}`);
    }
    
    // Test collaborators
    await testEndpoint('POST', `/shopping-lists/${listId}/collaborators`, {
      name: 'Test User'
    });
    
    // Clean up
    await testEndpoint('DELETE', `/shopping-lists/${listId}`);
  }
  
  console.log('\n✨ API testing completed!');
  console.log('\n💡 Note: Some tests may fail if MongoDB is not running.');
  console.log('   Products API should work with mock data even without database.');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  console.log('   Or install node-fetch: npm install node-fetch');
  process.exit(1);
}

runTests().catch(console.error);
