#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update existing products in MongoDB with brand and pricing information
 */

const { MongoClient } = require('mongodb');
const { BASE_PRODUCTS } = require('../data/base-products');
require('dotenv').config();

async function updateProductsWithBrands() {
  const client = new MongoClient(process.env.MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db('smart_shopper_db');
    const productsCollection = db.collection('products');
    
    console.log('🔄 Updating products with brands and pricing...');
    
    for (const product of BASE_PRODUCTS) {
      const updateResult = await productsCollection.updateOne(
        { id: product.id },
        { 
          $set: {
            brands: product.brands,
            defaultUnit: product.defaultUnit,
            estimatedPrice: product.estimatedPrice
          }
        }
      );
      
      if (updateResult.matchedCount > 0) {
        console.log(`✅ Updated ${product.name} with ${product.brands.length} brands`);
      } else {
        console.log(`⚠️  Product ${product.name} (${product.id}) not found in database`);
      }
    }
    
    console.log('\n📊 Verification - checking updated products:');
    const updatedProducts = await productsCollection.find({}).toArray();
    
    for (const product of updatedProducts) {
      console.log(`${product.name}: ${product.brands ? product.brands.length + ' brands' : 'No brands'} - $${product.estimatedPrice || 'No price'}`);
    }
    
    console.log('\n🎉 Product update completed!');
    
  } catch (error) {
    console.error('❌ Error updating products:', error);
  } finally {
    await client.close();
  }
}

if (require.main === module) {
  updateProductsWithBrands();
}

module.exports = updateProductsWithBrands;
