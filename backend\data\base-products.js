// Base product catalog - generic products that exist across supermarkets
const BASE_PRODUCTS = [
  {
    id: 'prod_milk_1l',
    name: 'Milk',
    iconName: 'MilkIcon',
    category: 'Dairy & Alternatives',
    imageUrl: 'https://picsum.photos/seed/milk_product/200/200',
    description: 'Fresh milk 1 litre'
  },
  {
    id: 'prod_bread_white',
    name: 'White Bread',
    iconName: 'BreadIcon',
    category: 'Bakery',
    imageUrl: 'https://picsum.photos/seed/bread_product/200/200',
    description: 'White sandwich bread loaf'
  },
  {
    id: 'prod_eggs_dozen',
    name: 'Eggs',
    iconName: 'EggIcon',
    category: 'Dairy & Alternatives',
    imageUrl: 'https://picsum.photos/seed/eggs_product/200/200',
    description: 'Free range eggs dozen'
  },
  {
    id: 'prod_bananas',
    name: '<PERSON><PERSON><PERSON>',
    iconName: 'AppleIcon',
    category: 'Produce',
    imageUrl: 'https://picsum.photos/seed/bananas_product/200/200',
    description: 'Fresh bananas'
  },
  {
    id: 'prod_chicken_breast',
    name: 'Chicken Breast',
    iconName: 'TagIcon',
    category: 'Meat & Seafood',
    imageUrl: 'https://picsum.photos/seed/chicken_product/200/200',
    description: 'Fresh chicken breast fillets'
  },
  {
    id: 'prod_rice_1kg',
    name: 'Rice',
    iconName: 'TagIcon',
    category: 'Pantry',
    imageUrl: 'https://picsum.photos/seed/rice_product/200/200',
    description: 'Long grain white rice 1kg'
  },
  {
    id: 'prod_pasta_500g',
    name: 'Pasta',
    iconName: 'TagIcon',
    category: 'Pantry',
    imageUrl: 'https://picsum.photos/seed/pasta_product/200/200',
    description: 'Spaghetti pasta 500g'
  },
  {
    id: 'prod_tomatoes',
    name: 'Tomatoes',
    iconName: 'AppleIcon',
    category: 'Produce',
    imageUrl: 'https://picsum.photos/seed/tomatoes_product/200/200',
    description: 'Fresh tomatoes'
  },
  {
    id: 'prod_cheese_tasty',
    name: 'Tasty Cheese',
    iconName: 'TagIcon',
    category: 'Dairy & Alternatives',
    imageUrl: 'https://picsum.photos/seed/cheese_product/200/200',
    description: 'Tasty cheese block 500g'
  },
  {
    id: 'prod_olive_oil',
    name: 'Olive Oil',
    iconName: 'TagIcon',
    category: 'Pantry',
    imageUrl: 'https://picsum.photos/seed/oil_product/200/200',
    description: 'Extra virgin olive oil 500ml'
  }
];

module.exports = { BASE_PRODUCTS };
