// Base product catalog - generic products that exist across supermarkets
const BASE_PRODUCTS = [
  {
    id: 'prod_milk_1l',
    name: 'Milk',
    iconName: 'MilkIcon',
    category: 'Dairy & Alternatives',
    imageUrl: 'https://picsum.photos/seed/milk_product/200/200',
    description: 'Fresh milk 1 litre',
    brands: ['Anchor', 'Meadow Fresh', 'Pams', 'Homebrand'],
    defaultUnit: 'litre',
    estimatedPrice: 3.00
  },
  {
    id: 'prod_bread_white',
    name: 'White Bread',
    iconName: 'BreadIcon',
    category: 'Bakery',
    imageUrl: 'https://picsum.photos/seed/bread_product/200/200',
    description: 'White sandwich bread loaf',
    brands: ['Tip Top', 'Molenberg', 'Pams', 'Homebrand'],
    defaultUnit: 'loaf',
    estimatedPrice: 2.50
  },
  {
    id: 'prod_eggs_dozen',
    name: 'Eggs',
    iconName: 'EggIcon',
    category: 'Dairy & Alternatives',
    imageUrl: 'https://picsum.photos/seed/eggs_product/200/200',
    description: 'Free range eggs dozen',
    brands: ['Woodland', 'Frenz', 'Pams Free Range', 'Homebrand'],
    defaultUnit: 'dozen',
    estimatedPrice: 6.50
  },
  {
    id: 'prod_bananas',
    name: 'Bananas',
    iconName: 'AppleIcon',
    category: 'Produce',
    imageUrl: 'https://picsum.photos/seed/bananas_product/200/200',
    description: 'Fresh bananas',
    brands: ['Dole', 'Chiquita', 'Local NZ', 'Organic'],
    defaultUnit: 'kg',
    estimatedPrice: 3.99
  },
  {
    id: 'prod_chicken_breast',
    name: 'Chicken Breast',
    iconName: 'TagIcon',
    category: 'Meat & Seafood',
    imageUrl: 'https://picsum.photos/seed/chicken_product/200/200',
    description: 'Fresh chicken breast fillets',
    brands: ['Tegel', 'Inghams', 'Free Range Farms', 'Organic'],
    defaultUnit: 'kg',
    estimatedPrice: 12.99
  },
  {
    id: 'prod_rice_1kg',
    name: 'Rice',
    iconName: 'TagIcon',
    category: 'Pantry',
    imageUrl: 'https://picsum.photos/seed/rice_product/200/200',
    description: 'Long grain white rice 1kg',
    brands: ['SunRice', 'Pams', 'Homebrand', 'Jasmine'],
    defaultUnit: 'kg',
    estimatedPrice: 4.50
  },
  {
    id: 'prod_pasta_500g',
    name: 'Pasta',
    iconName: 'TagIcon',
    category: 'Pantry',
    imageUrl: 'https://picsum.photos/seed/pasta_product/200/200',
    description: 'Spaghetti pasta 500g',
    brands: ['Barilla', 'San Remo', 'Pams', 'Homebrand'],
    defaultUnit: 'pack',
    estimatedPrice: 2.00
  },
  {
    id: 'prod_tomatoes',
    name: 'Tomatoes',
    iconName: 'AppleIcon',
    category: 'Produce',
    imageUrl: 'https://picsum.photos/seed/tomatoes_product/200/200',
    description: 'Fresh tomatoes',
    brands: ['Local NZ', 'Greenhouse', 'Organic', 'Cherry'],
    defaultUnit: 'kg',
    estimatedPrice: 5.99
  },
  {
    id: 'prod_cheese_tasty',
    name: 'Tasty Cheese',
    iconName: 'TagIcon',
    category: 'Dairy & Alternatives',
    imageUrl: 'https://picsum.photos/seed/cheese_product/200/200',
    description: 'Tasty cheese block 500g',
    brands: ['Mainland', 'Anchor', 'Pams', 'Homebrand'],
    defaultUnit: 'block',
    estimatedPrice: 8.50
  },
  {
    id: 'prod_olive_oil',
    name: 'Olive Oil',
    iconName: 'TagIcon',
    category: 'Pantry',
    imageUrl: 'https://picsum.photos/seed/oil_product/200/200',
    description: 'Extra virgin olive oil 500ml',
    brands: ['Cobram Estate', 'Bertolli', 'Pams', 'Homebrand'],
    defaultUnit: 'bottle',
    estimatedPrice: 12.00
  }
];

module.exports = { BASE_PRODUCTS };
