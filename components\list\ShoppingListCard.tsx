
import React from 'react';
import { Link } from 'react-router-dom';
import { ShoppingList } from '../../types';
import { TrashIcon, PencilIcon, ShareIcon } from '../../constants'; // Added ShareIcon
import { LIST_THEME_COLORS } from '../../constants';

interface ShoppingListCardProps {
  list: ShoppingList;
  onDelete: (listId: string) => void;
  onShare: (listId: string, listName: string) => void; // Added onShare prop
}

const ShoppingListCard: React.FC<ShoppingListCardProps> = ({ list, onDelete, onShare }) => {
  const itemCount = list.items.length;
  const completedItems = list.items.filter(item => item.isChecked).length;
  const progress = itemCount > 0 ? (completedItems / itemCount) * 100 : 0;
  
  const themeBgClass = LIST_THEME_COLORS[list.themeColor] || 'bg-primary';

  return (
    <div className={`rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-200 ease-in-out bg-white dark:bg-neutral-darker`}>
      <div className={`${themeBgClass} p-4 text-white relative`}>
        {list.icon && <list.icon className="w-12 h-12 opacity-20 absolute top-2 right-2" />}
        <h3 className="text-2xl font-bold mb-1">{list.name}</h3>
        <p className="text-sm opacity-90">{itemCount} item{itemCount !== 1 ? 's' : ''}</p>
        <div className="mt-3 flex -space-x-2">
          {list.collaborators.slice(0, 3).map(collab => (
            <img key={collab.id} className="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-neutral-darker" src={collab.avatarUrl} alt={collab.name}/>
          ))}
          {list.collaborators.length > 3 && (
            <span className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 text-xs font-medium text-gray-700 dark:text-gray-200 ring-2 ring-white dark:ring-neutral-darker">
              +{list.collaborators.length - 3}
            </span>
          )}
        </div>
      </div>
      
      <div className="p-4">
        <div className="mb-3">
          <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
            <span>Progress</span>
            <span>{completedItems} / {itemCount}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
            <div className={`${themeBgClass} h-2.5 rounded-full`} style={{ width: `${progress}%` }}></div>
          </div>
        </div>

        <Link to={`/list/${list.id}`}>
          <button className={`w-full ${themeBgClass} hover:opacity-90 text-white font-bold py-2 px-4 rounded-md transition-opacity`}>
            View List
          </button>
        </Link>
        <div className="mt-3 flex justify-end space-x-2">
            <button 
              onClick={() => onShare(list.id, list.name)} 
              className="p-2 text-gray-500 hover:text-green-500 dark:hover:text-green-400" 
              title="Share List"
            >
                <ShareIcon className="h-5 w-5"/>
            </button>
            <button onClick={() => alert('Edit feature coming soon!')} className="p-2 text-gray-500 hover:text-blue-500 dark:hover:text-blue-400" title="Edit List">
                <PencilIcon className="h-5 w-5"/>
            </button>
            <button onClick={() => onDelete(list.id)} className="p-2 text-gray-500 hover:text-red-500 dark:hover:text-red-400" title="Delete List">
                <TrashIcon className="h-5 w-5"/>
            </button>
        </div>
      </div>
    </div>
  );
};

export default ShoppingListCard;
