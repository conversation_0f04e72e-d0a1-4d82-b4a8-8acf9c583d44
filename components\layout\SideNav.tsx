
import React from 'react';
import { Link } from 'react-router-dom';
import { XMarkIcon, UserCircleIcon, BookOpenIcon, ShoppingCartIcon, HomeIcon } from '../../constants'; // Added HomeIcon for My Lists

interface SideNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const SideNav: React.FC<SideNavProps> = ({ isOpen, onClose }) => {
  return (
    <>
      {/* Overlay */}
      <div
        className={`fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Side Navigation Panel */}
      <div
        className={`fixed top-0 left-0 z-50 h-full w-72 bg-white dark:bg-neutral-darker shadow-xl transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="sidenav-title"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <Link to="/lists" className="flex items-center space-x-2 text-xl font-bold text-primary" onClick={onClose}>
              <ShoppingCartIcon className="h-7 w-7" />
              <h2 id="sidenav-title">Smart Shopper</h2>
            </Link>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 rounded-md hover:bg-gray-100 dark:hover:bg-neutral-dark focus:outline-none focus:ring-2 focus:ring-primary"
              aria-label="Close side navigation"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Navigation Links */}
          <nav className="flex-grow p-4 space-y-2">
            <Link
              to="/lists"
              onClick={onClose}
              className="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-neutral-dark rounded-md transition-colors"
            >
              <HomeIcon className="h-5 w-5 mr-3" />
              My Lists
            </Link>
            <a
              href="#" // Replace with actual link or routing logic
              onClick={(e) => {
                e.preventDefault();
                alert('Login / Create Account feature coming soon!');
                onClose();
              }}
              className="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-neutral-dark rounded-md transition-colors"
            >
              <UserCircleIcon className="h-5 w-5 mr-3" />
              Login / Create Account
            </a>
            <a
              href="#" // Replace with actual link or routing logic
              onClick={(e) => {
                e.preventDefault();
                alert('Recipes feature coming soon!');
                onClose();
              }}
              className="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-neutral-dark rounded-md transition-colors"
            >
              <BookOpenIcon className="h-5 w-5 mr-3" />
              Recipes
            </a>
          </nav>

          {/* Footer (optional) */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400">© 2024 Smart Shopper</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default SideNav;
