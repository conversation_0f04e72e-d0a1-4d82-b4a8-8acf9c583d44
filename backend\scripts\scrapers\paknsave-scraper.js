const BaseScraper = require('./base-scraper');

class PaknsaveScraper extends BaseScraper {
  constructor() {
    super('paknsave', 'https://www.paknsave.co.nz');
  }

  async extractProductData(baseProductId) {
    try {
      // Extract product information from Pak'nSave page
      const productData = await this.page.evaluate((baseProductId, supermarketId) => {
        // Common selectors for Pak'nSave (these may need adjustment)
        const nameElement = document.querySelector('h1, .product-title, [data-testid="product-title"]');
        const priceElement = document.querySelector('.price, .product-price, [data-testid="price"]');
        const brandElement = document.querySelector('.brand, .product-brand');
        const sizeElement = document.querySelector('.size, .product-size, .pack-size');
        const imageElement = document.querySelector('.product-image img, .hero-image img');
        
        const name = nameElement ? nameElement.textContent.trim() : '';
        const priceText = priceElement ? priceElement.textContent.trim() : '0';
        const brand = brandElement ? brandElement.textContent.trim() : null;
        const size = sizeElement ? sizeElement.textContent.trim() : null;
        const imageUrl = imageElement ? imageElement.src : null;
        
        // Extract price number
        const price = parseFloat(priceText.replace(/[^0-9.]/g, '')) || 0;
        
        return {
          id: `pns_${baseProductId}_${Date.now().toString().slice(-6)}`,
          productId: baseProductId,
          supermarketId: supermarketId,
          name: name,
          brand: brand,
          price: price,
          unit: 'each', // Default, may need logic to determine
          size: size,
          isAvailable: price > 0,
          lastUpdated: Date.now(),
          productUrl: window.location.href,
          imageUrl: imageUrl
        };
      }, baseProductId, this.supermarketId);

      return productData;
    } catch (error) {
      console.error('Error extracting Pak\'nSave product data:', error);
      return null;
    }
  }

  async scrapeCategory(categoryUrl, productMapping) {
    console.log(`Scraping Pak'nSave category: ${categoryUrl}`);
    
    await this.page.goto(categoryUrl, { waitUntil: 'networkidle2' });
    
    // Get all product links from category page
    const productLinks = await this.page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a[href*="/product/"]'));
      return links.map(link => link.href).filter(href => href.includes('/product/'));
    });

    const products = [];
    
    for (const link of productLinks.slice(0, 10)) { // Limit for testing
      const baseProductId = this.mapUrlToProductId(link, productMapping);
      if (baseProductId) {
        const product = await this.scrapeProduct(link, baseProductId);
        if (product) {
          products.push(product);
        }
        
        // Be respectful - wait between requests
        await this.page.waitForTimeout(1000);
      }
    }
    
    return products;
  }

  mapUrlToProductId(url, mapping) {
    // Map product URLs to base product IDs
    for (const [productId, keywords] of Object.entries(mapping)) {
      if (keywords.some(keyword => url.toLowerCase().includes(keyword.toLowerCase()))) {
        return productId;
      }
    }
    return null;
  }
}

module.exports = PaknsaveScraper;
