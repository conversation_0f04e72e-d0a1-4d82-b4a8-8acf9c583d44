
import React from 'react';
import { Link } from 'react-router-dom';
import ThemeToggle from '../ui/ThemeToggle';
import { ShoppingCartIcon, Bars3Icon } from '../../constants'; // Import Bars3Icon

interface HeaderProps {
  onToggleSideNav: () => void;
}

const Header: React.FC<HeaderProps> = ({ onToggleSideNav }) => {
  return (
    <header className="bg-white dark:bg-neutral-darker shadow-md sticky top-0 z-30"> {/* Ensure z-index is lower than SideNav overlay */}
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <button
            onClick={onToggleSideNav}
            className="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-neutral-dark focus:outline-none focus:ring-2 focus:ring-primary" // Removed lg:hidden
            aria-label="Open side navigation"
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          <Link to="/lists" className="flex items-center space-x-2 text-2xl font-bold text-primary">
            <ShoppingCartIcon className="h-8 w-8" />
            <span>Smart Shopper</span>
          </Link>
        </div>
        <div className="flex items-center space-x-3">
          {/* Mock user avatars */}
          <div className="flex -space-x-2">
            <img className="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-neutral-darker" src="https://picsum.photos/seed/user1/40/40" alt="User 1"/>
            <img className="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-neutral-darker" src="https://picsum.photos/seed/user2/40/40" alt="User 2"/>
          </div>
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
};

export default Header;