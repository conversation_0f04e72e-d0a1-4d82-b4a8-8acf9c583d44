const axios = require('axios');
const fs = require('fs');
const path = require('path');

class SupermarketAPI {
  constructor(supermarketId, config) {
    this.supermarketId = supermarketId;
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: config.headers || {},
      timeout: 30000
    });
  }

  async fetchProducts(productIds) {
    const products = [];
    
    for (const productId of productIds) {
      try {
        const product = await this.fetchSingleProduct(productId);
        if (product) {
          products.push(product);
        }
        
        // Rate limiting
        await this.delay(this.config.rateLimitMs || 1000);
      } catch (error) {
        console.error(`Error fetching ${productId}:`, error.message);
      }
    }
    
    return products;
  }

  async fetchSingleProduct(baseProductId) {
    // Override in subclasses
    throw new Error('fetchSingleProduct must be implemented');
  }

  async saveProducts(products) {
    const outputPath = path.join(__dirname, '..', '..', 'data', 'supermarkets', `${this.supermarketId}-products.js`);
    const constantName = `${this.supermarketId.toUpperCase()}_PRODUCTS`;
    
    const jsContent = `// ${this.supermarketId} product catalog with pricing - Updated ${new Date().toISOString()}
const ${constantName} = ${JSON.stringify(products, null, 2)};

module.exports = { ${constantName} };`;

    fs.writeFileSync(outputPath, jsContent);
    console.log(`✅ Updated ${products.length} products for ${this.supermarketId}`);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateProductId(baseProductId) {
    return `${this.supermarketId}_${baseProductId}_${Date.now().toString().slice(-6)}`;
  }
}

// Example implementation for a hypothetical API
class CountdownAPI extends SupermarketAPI {
  constructor() {
    super('countdown', {
      baseUrl: 'https://api.countdown.co.nz/v1',
      headers: {
        'User-Agent': 'ShoppingApp/1.0',
        'Accept': 'application/json'
      },
      rateLimitMs: 500
    });
  }

  async fetchSingleProduct(baseProductId) {
    try {
      // Map base product ID to store-specific search terms
      const searchTerm = this.mapProductIdToSearchTerm(baseProductId);
      
      const response = await this.client.get(`/products/search`, {
        params: {
          q: searchTerm,
          limit: 1
        }
      });

      const apiProduct = response.data.products?.[0];
      if (!apiProduct) return null;

      return {
        id: this.generateProductId(baseProductId),
        productId: baseProductId,
        supermarketId: this.supermarketId,
        name: apiProduct.name,
        brand: apiProduct.brand,
        price: apiProduct.price?.amount || 0,
        unit: apiProduct.unit || 'each',
        size: apiProduct.size,
        isAvailable: apiProduct.available !== false,
        lastUpdated: Date.now(),
        productUrl: apiProduct.url,
        imageUrl: apiProduct.image
      };
    } catch (error) {
      console.error(`API error for ${baseProductId}:`, error.message);
      return null;
    }
  }

  mapProductIdToSearchTerm(productId) {
    const mapping = {
      'prod_milk_1l': 'milk 1L',
      'prod_bread_white': 'white bread',
      'prod_eggs_dozen': 'eggs dozen',
      'prod_bananas': 'bananas',
      'prod_chicken_breast': 'chicken breast'
    };
    
    return mapping[productId] || productId;
  }
}

module.exports = { SupermarketAPI, CountdownAPI };
