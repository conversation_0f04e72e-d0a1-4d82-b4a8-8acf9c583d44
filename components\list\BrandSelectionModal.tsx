
import React, { useState, useEffect } from 'react';
import Modal from '../ui/Modal';
import Select from '../ui/Select';
import Button from '../ui/Button';
import { ShoppingListItem } from '../../types';
import { productCatalogService } from '../../services/productCatalogService';

interface BrandSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: ShoppingListItem | null; // Item for which brand is being selected
  availableBrands: string[];
  onSaveBrand: (itemId: string, brand: string) => void;
}

const BrandSelectionModal: React.FC<BrandSelectionModalProps> = ({
  isOpen,
  onClose,
  item,
  availableBrands,
  onSaveBrand,
}) => {
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [productImageUrl, setProductImageUrl] = useState<string | null | undefined>(null); // null for not fetched, undefined if no image
  const [isLoadingImage, setIsLoadingImage] = useState(false);

  useEffect(() => {
    if (isOpen && item?.productId) {
      setIsLoadingImage(true);
      setProductImageUrl(null); // Reset before fetching
      productCatalogService.getProductById(item.productId)
        .then(product => {
          setProductImageUrl(product?.imageUrl);
        })
        .catch(err => {
          console.error("Error fetching product image for modal:", err);
          setProductImageUrl(undefined); // Indicate error or no image
        })
        .finally(() => {
          setIsLoadingImage(false);
        });
    } else if (isOpen) {
        setProductImageUrl(undefined); // No product ID, so no image
    }


    if (isOpen && availableBrands.length > 0) {
      setSelectedBrand(item?.brand || ''); // Set to empty if no brand, let user choose
    } else if (isOpen) {
      setSelectedBrand('');
    }

    if (!isOpen) { // Reset on close
        setProductImageUrl(null);
        setIsLoadingImage(false);
    }

  }, [isOpen, item, availableBrands]);

  if (!isOpen || !item) return null;

  const brandOptions = availableBrands.map(b => ({ value: b, label: b }));

  const handleSave = () => {
    if (selectedBrand) {
      onSaveBrand(item.id, selectedBrand);
    } else {
        // If no brand selected but user clicks save, it's like skipping.
        // Or, disable save button if no brand is selected (current behavior based on `!selectedBrand` disabled state)
        onSaveBrand(item.id, ''); // Save as empty brand string if desired, or handle in parent
    }
    onClose(); 
  };
  
  const handleSkip = () => {
    onClose();
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Select Brand for ${item.name}`}
    >
      <div className="space-y-4">
        {isLoadingImage && (
            <div className="flex justify-center items-center h-24">
                <p className="text-gray-500 dark:text-gray-400">Loading image...</p>
            </div>
        )}
        {!isLoadingImage && productImageUrl && (
          <div className="flex justify-center mb-4">
            <img 
                src={productImageUrl} 
                alt={`Image of ${item.name}`} 
                className="max-h-32 max-w-full object-contain rounded-md shadow-sm" 
            />
          </div>
        )}
        {!isLoadingImage && productImageUrl === undefined && item?.productId && (
             <div className="flex justify-center items-center h-24">
                <p className="text-xs text-gray-400 dark:text-gray-500">No image available for this product.</p>
            </div>
        )}

        {brandOptions.length > 0 ? (
          <Select
            label="Brand"
            options={brandOptions}
            value={selectedBrand}
            onChange={(e) => setSelectedBrand(e.target.value)}
            placeholderOption="-- Choose a brand --"
          />
        ) : (
          <p className="text-gray-600 dark:text-gray-400">No specific brands listed for this product. You can skip this step.</p>
        )}
      </div>
      <div className="flex justify-end space-x-3 pt-6">
        <Button type="button" variant="ghost" onClick={handleSkip}>
          {brandOptions.length > 0 ? 'Skip / No Brand' : 'Close'}
        </Button>
        {brandOptions.length > 0 && (
           <Button type="button" variant="primary" onClick={handleSave} disabled={!selectedBrand}>
            Save Brand
          </Button>
        )}
      </div>
    </Modal>
  );
};

export default BrandSelectionModal;