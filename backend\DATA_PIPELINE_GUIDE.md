# 🔄 Data Pipeline Guide

This guide explains how to funnel product data from New Zealand supermarkets into your app.

## 📋 Quick Start Options

### Option 1: Manual CSV Import (Easiest)

1. **Create CSV files** for each supermarket:
```bash
# Use the template
cp backend/data/templates/product-template.csv paknsave-data.csv
```

2. **Edit the CSV** with real product data:
```csv
productId,supermarketId,name,brand,price,unit,size,isAvailable,productUrl
prod_milk_1l,paknsave,Anchor Blue Milk 1L,Anchor,2.89,each,1L,true,https://www.paknsave.co.nz/...
```

3. **Import the data**:
```bash
cd backend
node scripts/csv-to-js.js paknsave-data.csv paknsave
```

### Option 2: JSON Import

1. **Create JSON file**:
```json
{
  "products": [
    {
      "productId": "prod_milk_1l",
      "name": "<PERSON>chor Blue Milk 1L",
      "brand": "Anchor",
      "price": 2.89,
      "unit": "each",
      "size": "1L",
      "isAvailable": true
    }
  ]
}
```

2. **Import**:
```bash
node scripts/data-importer.js json paknsave paknsave-products.json
```

## 🤖 Automated Data Collection

### Web Scraping Setup

1. **Install dependencies**:
```bash
npm install puppeteer node-cron
```

2. **Configure scraper** for each supermarket:
```javascript
// Customize backend/scripts/scrapers/paknsave-scraper.js
const scraper = new PaknsaveScraper();
await scraper.init();

const products = await scraper.scrapeCategory(
  'https://www.paknsave.co.nz/shop/category/dairy-eggs',
  {
    'prod_milk_1l': ['milk', '1l', 'anchor'],
    'prod_eggs_dozen': ['eggs', 'dozen', 'free-range']
  }
);
```

3. **Run scraper**:
```bash
node scripts/scrapers/paknsave-scraper.js
```

### API Integration

1. **Set up API credentials** (if available):
```bash
# .env file
PAKNSAVE_API_KEY=your_api_key
NEWWORLD_API_URL=https://api.newworld.co.nz
```

2. **Configure API connector**:
```javascript
const api = new SupermarketAPI('paknsave', {
  baseUrl: 'https://api.paknsave.co.nz',
  headers: { 'Authorization': `Bearer ${process.env.PAKNSAVE_API_KEY}` }
});
```

## 📊 Real-time Price Monitoring

### Setup Automated Updates

1. **Start price monitor**:
```bash
node scripts/price-monitor.js start
```

2. **Schedule updates** (runs every 6 hours by default):
```bash
# Set custom interval in .env
PRICE_UPDATE_INTERVAL="0 */2 * * *"  # Every 2 hours
```

3. **Manual update**:
```bash
node scripts/price-monitor.js update
```

## 🏪 Supermarket-Specific Instructions

### Pak'nSave
- **Website**: https://www.paknsave.co.nz
- **Strategy**: Web scraping (no public API)
- **Rate limit**: 1 request per second
- **Best time**: Off-peak hours (2-6 AM)

### New World  
- **Website**: https://www.newworld.co.nz
- **Strategy**: Web scraping + potential API
- **Rate limit**: 1 request per second
- **Notes**: Same parent company as Pak'nSave

### Woolworths
- **Website**: https://www.woolworths.co.nz  
- **Strategy**: Web scraping
- **Rate limit**: 1 request per second
- **Notes**: May have mobile app API

### The Warehouse
- **Website**: https://www.thewarehouse.co.nz
- **Strategy**: Web scraping (limited grocery selection)
- **Rate limit**: 1 request per second
- **Notes**: Focus on packaged goods, limited fresh produce

## 🔧 Data Validation & Quality

### Validate Data
```bash
node scripts/data-importer.js validate paknsave
```

### Data Quality Checks
- ✅ All products have valid prices
- ✅ Product IDs match base products
- ✅ Availability status is boolean
- ✅ URLs are accessible
- ✅ Images load correctly

## 📈 Monitoring & Maintenance

### Generate Reports
```bash
node scripts/price-monitor.js report
```

### Check Data Freshness
```bash
# Products older than 24 hours
node -e "
const products = require('./data/supermarkets/paknsave-products.js').PAKNSAVE_PRODUCTS;
const stale = products.filter(p => Date.now() - p.lastUpdated > 86400000);
console.log(\`Stale products: \${stale.length}\`);
"
```

## 🚨 Legal & Ethical Considerations

### Web Scraping Guidelines
- ✅ Respect robots.txt
- ✅ Use reasonable rate limits
- ✅ Don't overload servers
- ✅ Cache data appropriately
- ✅ Identify your bot in User-Agent

### Terms of Service
- 📖 Read each supermarket's ToS
- 🤝 Consider reaching out for API access
- ⚖️ Ensure compliance with NZ law
- 🛡️ Implement proper error handling

## 🔄 Data Flow Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Supermarket   │───▶│  Data Source │───▶│   Your App      │
│   Websites/APIs │    │  (Scraper/API)│    │   Database      │
└─────────────────┘    └──────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────┐
                       │  Validation  │
                       │  & Transform │
                       └──────────────┘
                              │
                              ▼
                       ┌──────────────┐
                       │   MongoDB    │
                       │  + JS Files  │
                       └──────────────┘
```

## 🎯 Next Steps

1. **Start with manual CSV import** for immediate testing
2. **Set up web scraping** for one supermarket (recommend Pak'nSave)
3. **Implement price monitoring** for automated updates
4. **Add data validation** and error handling
5. **Scale to all supermarkets** gradually
6. **Consider API partnerships** for official data access

## 📞 Getting Help

- Check logs in `backend/logs/`
- Validate data with built-in tools
- Monitor price update frequency
- Test with small product sets first

Remember: Start simple with manual data entry, then gradually automate as you understand each supermarket's structure! 🚀
