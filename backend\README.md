# Smart Shopper Backend API

A Node.js/Express backend API for the Smart Shopper shopping list application with MongoDB integration.

## Features

- Complete CRUD operations for shopping lists
- Shopping list item management
- Collaborator management
- Product catalog with search functionality
- MongoDB integration
- Error handling and validation
- Health check endpoint

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your MongoDB connection string.

3. **Start MongoDB:**

   **Option A: Local MongoDB Installation**
   - Download and install MongoDB Community Server from [mongodb.com](https://www.mongodb.com/try/download/community)
   - Start MongoDB service:
     - Windows: MongoDB should start automatically as a service
     - macOS: `brew services start mongodb/brew/mongodb-community`
     - Linux: `sudo systemctl start mongod`
   - Verify it's running on `mongodb://localhost:27017`

   **Option B: MongoDB Atlas (Cloud)**
   - Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Create a cluster and get your connection string
   - Update `.env` with your Atlas connection string

   **Option C: Docker**
   ```bash
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

   **Note:** The server will start in development mode even without MongoDB, but shopping list features will be limited.

4. **Run the server:**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Health Check
- `GET /api/health` - Server health status

### Shopping Lists
- `GET /api/shopping-lists` - Get all shopping lists
- `GET /api/shopping-lists/:listId` - Get specific shopping list
- `POST /api/shopping-lists` - Create new shopping list
- `DELETE /api/shopping-lists/:listId` - Delete shopping list

### Shopping List Items
- `POST /api/shopping-lists/:listId/items` - Add item to list
- `PUT /api/shopping-lists/:listId/items/:itemId` - Update item
- `DELETE /api/shopping-lists/:listId/items/:itemId` - Remove item
- `PATCH /api/shopping-lists/:listId/items/:itemId/toggle` - Toggle item checked state

### Collaborators
- `POST /api/shopping-lists/:listId/collaborators` - Add collaborator to list

### Products
- `GET /api/products` - Get all products
- `GET /api/products/search?q=term` - Search products by name
- `GET /api/products/:id` - Get product by ID

## Data Models

### Shopping List
```json
{
  "id": "string",
  "name": "string",
  "themeColor": "string",
  "iconName": "string",
  "items": [ShoppingListItem],
  "collaborators": [Collaborator],
  "createdAt": "number"
}
```

### Shopping List Item
```json
{
  "id": "string",
  "name": "string",
  "productId": "string",
  "brand": "string",
  "quantity": "number",
  "unit": "string",
  "pricePerUnit": "number",
  "isChecked": "boolean",
  "addedAt": "number"
}
```

### Product
```json
{
  "id": "string",
  "name": "string",
  "iconName": "string",
  "brands": ["string"],
  "defaultUnit": "string",
  "category": "string",
  "estimatedPrice": "number",
  "imageUrl": "string"
}
```

## Environment Variables

- `MONGODB_URI` - MongoDB connection string
- `PORT` - Server port (default: 5000)
- `NODE_ENV` - Environment mode (development/production)

## Database Collections

- `shopping_lists` - Shopping list documents
- `products` - Product catalog documents
