# Smart Shopper Backend API

A Node.js/Express backend API for the Smart Shopper shopping list application with MongoDB integration.

## Features

- Complete CRUD operations for shopping lists
- Shopping list item management
- Collaborator management
- **Multi-supermarket product catalog** (Pak'nSave, New World, Woolworths, The Warehouse)
- **Price comparison across supermarkets**
- **Shopping list total cost comparison**
- Product search functionality
- MongoDB integration with fallback to mock data
- Error handling and validation
- Health check endpoint

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your MongoDB connection string.

3. **Start MongoDB:**

   **Option A: Local MongoDB Installation**
   - Download and install MongoDB Community Server from [mongodb.com](https://www.mongodb.com/try/download/community)
   - Start MongoDB service:
     - Windows: MongoDB should start automatically as a service
     - macOS: `brew services start mongodb/brew/mongodb-community`
     - Linux: `sudo systemctl start mongod`
   - Verify it's running on `mongodb://localhost:27017`

   **Option B: MongoDB Atlas (Cloud)**
   - Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Create a cluster and get your connection string
   - Update `.env` with your Atlas connection string

   **Option C: Docker**
   ```bash
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

   **Note:** The server will start in development mode even without MongoDB, but shopping list features will be limited.

4. **Run the server:**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Health Check
- `GET /api/health` - Server health status

### Shopping Lists
- `GET /api/shopping-lists` - Get all shopping lists
- `GET /api/shopping-lists/:listId` - Get specific shopping list
- `POST /api/shopping-lists` - Create new shopping list
- `DELETE /api/shopping-lists/:listId` - Delete shopping list

### Shopping List Items
- `POST /api/shopping-lists/:listId/items` - Add item to list
- `PUT /api/shopping-lists/:listId/items/:itemId` - Update item
- `DELETE /api/shopping-lists/:listId/items/:itemId` - Remove item
- `PATCH /api/shopping-lists/:listId/items/:itemId/toggle` - Toggle item checked state

### Collaborators
- `POST /api/shopping-lists/:listId/collaborators` - Add collaborator to list

### Products
- `GET /api/products` - Get all base products
- `GET /api/products/search?q=term` - Search products by name
- `GET /api/products/:id` - Get product by ID
- `GET /api/products/:id/prices` - Get price comparison across supermarkets

### Supermarkets
- `GET /api/supermarkets` - Get all supermarkets
- `GET /api/supermarkets/:id/products` - Get products for specific supermarket

### Price Comparison
- `POST /api/shopping-lists/:id/price-comparison` - Get total cost comparison for shopping list

## Data Models

### Shopping List
```json
{
  "id": "string",
  "name": "string",
  "themeColor": "string",
  "iconName": "string",
  "items": [ShoppingListItem],
  "collaborators": [Collaborator],
  "createdAt": "number"
}
```

### Shopping List Item
```json
{
  "id": "string",
  "name": "string",
  "productId": "string",
  "brand": "string",
  "quantity": "number",
  "unit": "string",
  "pricePerUnit": "number",
  "isChecked": "boolean",
  "addedAt": "number"
}
```

### Product (Base)
```json
{
  "id": "string",
  "name": "string",
  "iconName": "string",
  "category": "string",
  "imageUrl": "string",
  "description": "string"
}
```

### Supermarket
```json
{
  "id": "string",
  "name": "string",
  "logo": "string",
  "color": "string",
  "website": "string"
}
```

### Supermarket Product
```json
{
  "id": "string",
  "productId": "string",
  "supermarketId": "string",
  "name": "string",
  "brand": "string",
  "price": "number",
  "unit": "string",
  "size": "string",
  "isAvailable": "boolean",
  "lastUpdated": "number",
  "productUrl": "string",
  "imageUrl": "string"
}
```

### Price Comparison
```json
{
  "productId": "string",
  "productName": "string",
  "prices": [
    {
      "supermarketId": "string",
      "supermarketName": "string",
      "price": "number",
      "unit": "string",
      "size": "string",
      "isAvailable": "boolean",
      "productUrl": "string"
    }
  ],
  "lowestPrice": {
    "supermarketId": "string",
    "price": "number"
  }
}
```

## Environment Variables

- `MONGODB_URI` - MongoDB connection string
- `PORT` - Server port (default: 5000)
- `NODE_ENV` - Environment mode (development/production)

## Database Collections

- `shopping_lists` - Shopping list documents
- `products` - Base product catalog documents
- `supermarkets` - Supermarket information documents
- `supermarket_products` - Supermarket-specific product pricing documents

## New Zealand Supermarkets Supported

- **Pak'nSave** - Budget-focused supermarket chain
- **New World** - Premium supermarket chain
- **Woolworths** - Major supermarket chain
- **The Warehouse** - General merchandise with limited grocery selection

## Example API Usage

### Get Price Comparison for Milk
```bash
curl http://localhost:5000/api/products/prod_milk_1l/prices
```

### Get All Pak'nSave Products
```bash
curl http://localhost:5000/api/supermarkets/paknsave/products
```

### Compare Shopping List Costs (requires MongoDB)
```bash
curl -X POST http://localhost:5000/api/shopping-lists/{listId}/price-comparison
```
