// Pak'nSave product catalog with pricing
const PAKNSAVE_PRODUCTS = [
  {
    id: 'pns_milk_1l_001',
    productId: 'prod_milk_1l',
    supermarketId: 'paknsave',
    name: 'Anchor Blue Milk 1L',
    brand: 'Anchor',
    price: 2.89,
    unit: 'each',
    size: '1L',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/anchor-blue-milk-1l',
    imageUrl: 'https://picsum.photos/seed/pns_milk/200/200'
  },
  {
    id: 'pns_bread_white_001',
    productId: 'prod_bread_white',
    supermarketId: 'paknsave',
    name: 'Tip Top White Sandwich Bread',
    brand: 'Tip Top',
    price: 1.99,
    unit: 'each',
    size: '700g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/tip-top-white-bread',
    imageUrl: 'https://picsum.photos/seed/pns_bread/200/200'
  },
  {
    id: 'pns_eggs_dozen_001',
    productId: 'prod_eggs_dozen',
    supermarketId: 'paknsave',
    name: 'Woodland Free Range Eggs Size 7',
    brand: 'Woodland',
    price: 6.49,
    unit: 'dozen',
    size: '12 pack',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/woodland-free-range-eggs',
    imageUrl: 'https://picsum.photos/seed/pns_eggs/200/200'
  },
  {
    id: 'pns_bananas_001',
    productId: 'prod_bananas',
    supermarketId: 'paknsave',
    name: 'Bananas',
    brand: null,
    price: 2.99,
    unit: 'kg',
    size: null,
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/bananas',
    imageUrl: 'https://picsum.photos/seed/pns_bananas/200/200'
  },
  {
    id: 'pns_chicken_breast_001',
    productId: 'prod_chicken_breast',
    supermarketId: 'paknsave',
    name: 'Fresh Chicken Breast Fillets',
    brand: 'Tegel',
    price: 12.99,
    unit: 'kg',
    size: null,
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/chicken-breast',
    imageUrl: 'https://picsum.photos/seed/pns_chicken/200/200'
  },
  {
    id: 'pns_rice_1kg_001',
    productId: 'prod_rice_1kg',
    supermarketId: 'paknsave',
    name: 'SunRice Long Grain White Rice',
    brand: 'SunRice',
    price: 3.49,
    unit: 'each',
    size: '1kg',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/sunrice-white-rice',
    imageUrl: 'https://picsum.photos/seed/pns_rice/200/200'
  },
  {
    id: 'pns_pasta_500g_001',
    productId: 'prod_pasta_500g',
    supermarketId: 'paknsave',
    name: 'Barilla Spaghetti',
    brand: 'Barilla',
    price: 2.19,
    unit: 'each',
    size: '500g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/barilla-spaghetti',
    imageUrl: 'https://picsum.photos/seed/pns_pasta/200/200'
  },
  {
    id: 'pns_tomatoes_001',
    productId: 'prod_tomatoes',
    supermarketId: 'paknsave',
    name: 'Tomatoes',
    brand: null,
    price: 4.99,
    unit: 'kg',
    size: null,
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/tomatoes',
    imageUrl: 'https://picsum.photos/seed/pns_tomatoes/200/200'
  },
  {
    id: 'pns_cheese_tasty_001',
    productId: 'prod_cheese_tasty',
    supermarketId: 'paknsave',
    name: 'Mainland Tasty Cheese Block',
    brand: 'Mainland',
    price: 7.99,
    unit: 'each',
    size: '500g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/mainland-tasty-cheese',
    imageUrl: 'https://picsum.photos/seed/pns_cheese/200/200'
  },
  {
    id: 'pns_olive_oil_001',
    productId: 'prod_olive_oil',
    supermarketId: 'paknsave',
    name: 'Olivado Extra Virgin Olive Oil',
    brand: 'Olivado',
    price: 8.99,
    unit: 'each',
    size: '500ml',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.paknsave.co.nz/shop/product/olivado-olive-oil',
    imageUrl: 'https://picsum.photos/seed/pns_oil/200/200'
  }
];

module.exports = { PAKNSAVE_PRODUCTS };
