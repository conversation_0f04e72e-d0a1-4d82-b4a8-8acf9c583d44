
import React, { useState } from 'react';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import { ShoppingList } from '../../types';
import { LIST_THEME_COLORS, HomeIcon, BuildingOfficeIcon, ShoppingCartIcon } from '../../constants';

interface CreateListModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateList: (listData: Pick<ShoppingList, 'name' | 'themeColor'> & {iconName?: string}) => Promise<void>;
}

const listIconOptions = [
  { name: 'Generic', Icon: ShoppingCartIcon, value: 'ShoppingCartIcon' },
  { name: 'Home', Icon: HomeIcon, value: 'HomeIcon' },
  { name: 'Office', Icon: BuildingOfficeIcon, value: 'BuildingOfficeIcon' },
];


const CreateListModal: React.FC<CreateListModalProps> = ({ isOpen, onClose, onCreateList }) => {
  const [listName, setListName] = useState('');
  const [selectedColor, setSelectedColor] = useState(Object.keys(LIST_THEME_COLORS)[0]);
  const [selectedIconName, setSelectedIconName] = useState(listIconOptions[0].value);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!listName.trim()) {
      alert("List name cannot be empty.");
      return;
    }
    setIsLoading(true);
    try {
      await onCreateList({ name: listName, themeColor: selectedColor, iconName: selectedIconName });
      setListName(''); // Reset form
      setSelectedColor(Object.keys(LIST_THEME_COLORS)[0]);
      setSelectedIconName(listIconOptions[0].value);
      onClose();
    } catch (error) {
      console.error("Failed to create list", error);
      alert("There was an error creating the list. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Create New Shopping List">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Input
          label="List Name"
          id="listName"
          value={listName}
          onChange={(e) => setListName(e.target.value)}
          placeholder="e.g., Weekly Groceries, Holiday Gifts"
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Theme Color</label>
          <div className="flex flex-wrap gap-2">
            {Object.entries(LIST_THEME_COLORS).map(([colorName, bgClass]) => (
              <button
                key={colorName}
                type="button"
                onClick={() => setSelectedColor(colorName)}
                className={`w-8 h-8 rounded-full ${bgClass} border-2 ${selectedColor === colorName ? 'ring-2 ring-offset-2 dark:ring-offset-neutral-darker ring-black dark:ring-white' : 'border-transparent'}`}
                aria-label={`Select ${colorName} theme`}
              />
            ))}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Icon</label>
          <div className="flex flex-wrap gap-2">
            {listIconOptions.map(({ name, Icon, value }) => (
              <button
                key={value}
                type="button"
                onClick={() => setSelectedIconName(value)}
                className={`p-2 rounded-md border-2 ${selectedIconName === value ? 'bg-primary/20 border-primary' : 'bg-gray-100 dark:bg-neutral-dark hover:bg-gray-200 dark:hover:bg-neutral-darker border-gray-300 dark:border-gray-600'}`}
                aria-label={`Select ${name} icon`}
              >
                <Icon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
              </button>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="ghost" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button type="submit" variant="primary" isLoading={isLoading} disabled={isLoading || !listName.trim()}>
            Create List
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateListModal;
    