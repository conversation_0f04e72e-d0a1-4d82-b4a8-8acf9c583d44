#!/usr/bin/env node

/**
 * Convert CSV files to JavaScript product files
 * Usage: node csv-to-js.js <csvFile> <outputFile>
 */

const fs = require('fs');
const path = require('path');

function csvToProducts(csvContent) {
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',');
  const products = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',');
    const product = {};
    
    headers.forEach((header, index) => {
      const value = values[index]?.trim();
      
      // Convert specific fields to appropriate types
      switch (header) {
        case 'price':
          product[header] = parseFloat(value) || 0;
          break;
        case 'isAvailable':
          product[header] = value.toLowerCase() === 'true';
          break;
        case 'lastUpdated':
          product[header] = Date.now();
          break;
        default:
          product[header] = value || null;
      }
    });
    
    // Generate unique ID if not provided
    if (!product.id) {
      product.id = `${product.supermarketId}_${product.productId}_${String(Date.now()).slice(-6)}`;
    }
    
    products.push(product);
  }
  
  return products;
}

function generateJSFile(products, supermarketName) {
  const constantName = `${supermarketName.toUpperCase()}_PRODUCTS`;
  
  return `// ${supermarketName} product catalog with pricing
const ${constantName} = ${JSON.stringify(products, null, 2)};

module.exports = { ${constantName} };`;
}

// Main execution
if (process.argv.length < 4) {
  console.log('Usage: node csv-to-js.js <csvFile> <supermarketName>');
  console.log('Example: node csv-to-js.js paknsave-data.csv paknsave');
  process.exit(1);
}

const csvFile = process.argv[2];
const supermarketName = process.argv[3];

try {
  const csvContent = fs.readFileSync(csvFile, 'utf8');
  const products = csvToProducts(csvContent);
  const jsContent = generateJSFile(products, supermarketName);
  
  const outputFile = path.join(__dirname, '..', 'data', 'supermarkets', `${supermarketName}-products.js`);
  fs.writeFileSync(outputFile, jsContent);
  
  console.log(`✅ Generated ${outputFile} with ${products.length} products`);
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}
