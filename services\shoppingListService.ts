
import { ShoppingList, ShoppingListItem, Collaborator } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

export const shoppingListService = {
  // GET /api/shopping-lists
  getAllLists: async (): Promise<ShoppingList[]> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists`);
    if (!response.ok) {
      throw new Error('Failed to fetch shopping lists');
    }
    return response.json();
  },

  // GET /api/shopping-lists/:listId
  getListById: async (listId: string): Promise<ShoppingList | undefined> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}`);
    if (!response.ok) {
      if (response.status === 404) return undefined;
      throw new Error('Failed to fetch shopping list');
    }
    return response.json();
  },

  // POST /api/shopping-lists
  createList: async (name: string, themeColor: string = 'blue', iconName?: string): Promise<ShoppingList> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, themeColor, iconName })
    });
    if (!response.ok) {
      throw new Error('Failed to create shopping list');
    }
    return response.json();
  },

  // DELETE /api/shopping-lists/:listId
  deleteList: async (listId: string): Promise<void> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}`, { method: 'DELETE' });
    if (!response.ok) {
      throw new Error('Failed to delete shopping list');
    }
  },

  // POST /api/shopping-lists/:listId/items
  addItemToList: async (listId: string, itemData: Omit<ShoppingListItem, 'id' | 'addedAt' | 'isChecked'>): Promise<ShoppingListItem | null> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}/items`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(itemData)
    });
    if (!response.ok) {
      throw new Error('Failed to add item to shopping list');
    }
    return response.json();
  },

  // PUT /api/shopping-lists/:listId/items/:itemId
  updateItemInList: async (listId: string, itemId: string, updates: Partial<ShoppingListItem>): Promise<ShoppingListItem | null> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}/items/${itemId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    if (!response.ok) {
      throw new Error('Failed to update item in shopping list');
    }
    return response.json();
  },

  // DELETE /api/shopping-lists/:listId/items/:itemId
  removeItemFromList: async (listId: string, itemId: string): Promise<void> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}/items/${itemId}`, { method: 'DELETE' });
    if (!response.ok) {
      throw new Error('Failed to remove item from shopping list');
    }
  },

  // PATCH /api/shopping-lists/:listId/items/:itemId/toggle
  toggleItemChecked: async (listId: string, itemId: string): Promise<ShoppingListItem | null> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}/items/${itemId}/toggle`, { method: 'PATCH' });
    if (!response.ok) {
      throw new Error('Failed to toggle item checked state');
    }
    return response.json();
  },

  // POST /api/shopping-lists/:listId/collaborators
  addCollaboratorToList: async (listId: string, collaboratorName: string): Promise<ShoppingList | null> => {
    const response = await fetch(`${API_BASE_URL}/shopping-lists/${listId}/collaborators`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: collaboratorName })
    });
    if (!response.ok) {
      throw new Error('Failed to add collaborator to shopping list');
    }
    return response.json();
  }
};

