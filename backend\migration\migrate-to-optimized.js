#!/usr/bin/env node

/**
 * Migration script to optimize your current MongoDB structure
 * Run this to improve performance and reduce data transfer
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

async function migrateToOptimized() {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  const db = client.db('smart_shopper_db');

  console.log('🔄 Starting data optimization migration...');

  // Step 1: Create optimized prices collection
  console.log('\n📊 Step 1: Optimizing prices collection...');
  
  const supermarketProducts = await db.collection('supermarket_products').find({}).toArray();
  
  const optimizedPrices = supermarketProducts.map(p => ({
    productId: p.productId,
    supermarketId: p.supermarketId,
    price: p.price,
    unit: p.unit,
    size: p.size,
    available: p.isAvailable,
    url: p.productUrl ? p.productUrl.replace(/https?:\/\/[^\/]+/, '') : null,
    updated: new Date(p.lastUpdated || Date.now())
  }));

  // Create new optimized collection
  await db.collection('prices').drop().catch(() => {});
  if (optimizedPrices.length > 0) {
    await db.collection('prices').insertMany(optimizedPrices);
  }

  // Create performance indexes
  await db.collection('prices').createIndexes([
    { key: { productId: 1, supermarketId: 1 }, unique: true, name: 'product_supermarket_unique' },
    { key: { supermarketId: 1, available: 1 }, name: 'supermarket_available' },
    { key: { productId: 1, price: 1 }, name: 'product_price_sort' },
    { key: { updated: -1 }, name: 'recent_updates' }
  ]);

  console.log(`✅ Created optimized prices collection with ${optimizedPrices.length} records`);

  // Step 2: Create compact supermarkets collection
  console.log('\n🏪 Step 2: Creating compact supermarkets...');
  
  const supermarkets = await db.collection('supermarkets').find({}).toArray();
  const compactSupermarkets = supermarkets.map(s => ({
    id: s.id,
    name: s.name,
    short: s.name.split(' ')[0], // For compact displays
    color: s.color,
    logo: s.logo ? s.logo.split('/').pop() : null // Just filename
  }));

  await db.collection('supermarkets_compact').drop().catch(() => {});
  if (compactSupermarkets.length > 0) {
    await db.collection('supermarkets_compact').insertMany(compactSupermarkets);
  }

  console.log(`✅ Created compact supermarkets with ${compactSupermarkets.length} records`);

  // Step 3: Create price comparison cache for popular products
  console.log('\n💰 Step 3: Creating price comparison cache...');
  
  const products = await db.collection('products').find({}).toArray();
  const priceComparisons = [];

  for (const product of products) {
    const prices = await db.collection('prices').find({ productId: product.id }).toArray();
    
    if (prices.length > 0) {
      const pricesWithNames = prices.map(p => {
        const supermarket = compactSupermarkets.find(s => s.id === p.supermarketId);
        return {
          supermarketId: p.supermarketId,
          supermarketName: supermarket?.name || p.supermarketId,
          price: p.price,
          unit: p.unit,
          size: p.size,
          available: p.available
        };
      });

      const availablePrices = pricesWithNames.filter(p => p.available);
      const lowestPrice = availablePrices.length > 0 
        ? availablePrices.reduce((min, curr) => curr.price < min.price ? curr : min)
        : null;

      priceComparisons.push({
        productId: product.id,
        productName: product.name,
        prices: pricesWithNames,
        lowestPrice: lowestPrice ? {
          supermarketId: lowestPrice.supermarketId,
          price: lowestPrice.price
        } : null,
        updated: new Date()
      });
    }
  }

  await db.collection('price_comparisons').drop().catch(() => {});
  if (priceComparisons.length > 0) {
    await db.collection('price_comparisons').insertMany(priceComparisons);
    await db.collection('price_comparisons').createIndex({ productId: 1 }, { unique: true });
  }

  console.log(`✅ Created price comparison cache with ${priceComparisons.length} products`);

  // Step 4: Create compact products for search
  console.log('\n📦 Step 4: Creating compact products for search...');
  
  const compactProducts = products.map(p => ({
    id: p.id,
    name: p.name,
    category: p.category,
    icon: p.iconName,
    image: p.imageUrl ? p.imageUrl.split('/').pop() : null
  }));

  await db.collection('products_search').drop().catch(() => {});
  if (compactProducts.length > 0) {
    await db.collection('products_search').insertMany(compactProducts);
    await db.collection('products_search').createIndex(
      { name: 'text', category: 'text' },
      { name: 'search_text' }
    );
  }

  console.log(`✅ Created search-optimized products with ${compactProducts.length} records`);

  // Step 5: Analyze space savings
  console.log('\n📈 Step 5: Analyzing optimization results...');
  
  const originalSize = await db.collection('supermarket_products').stats().then(s => s.storageSize).catch(() => 0);
  const optimizedSize = await db.collection('prices').stats().then(s => s.storageSize).catch(() => 0);
  const spaceSaved = originalSize - optimizedSize;
  
  console.log(`\n📊 Optimization Results:`);
  console.log(`   Original size: ${(originalSize / 1024).toFixed(2)} KB`);
  console.log(`   Optimized size: ${(optimizedSize / 1024).toFixed(2)} KB`);
  console.log(`   Space saved: ${(spaceSaved / 1024).toFixed(2)} KB (${((spaceSaved / originalSize) * 100).toFixed(1)}%)`);

  await client.close();
  console.log('\n🎉 Migration completed successfully!');
  console.log('\n📋 New Collections Created:');
  console.log('   • prices - Optimized price data');
  console.log('   • supermarkets_compact - Lightweight supermarket info');
  console.log('   • price_comparisons - Pre-calculated comparisons');
  console.log('   • products_search - Search-optimized product data');
}

if (require.main === module) {
  migrateToOptimized().catch(console.error);
}

module.exports = migrateToOptimized;
