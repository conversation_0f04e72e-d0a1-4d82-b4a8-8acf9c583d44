
import React, { useState, useEffect } from 'react';
import { ShoppingListItem, PriceComparison } from '../../types';
import { TrashIcon, CheckCircleIcon, CircleIcon, PencilIcon } from '../../constants';
import { priceComparisonService } from '../../services/priceComparisonService';

interface ShoppingListItemRowProps {
  item: ShoppingListItem;
  onToggleChecked: (itemId: string) => void;
  onRemoveItem: (itemId: string) => void;
  onEditItem: (item: ShoppingListItem) => void;
}

const ShoppingListItemRow: React.FC<ShoppingListItemRowProps> = ({ item, onToggleChecked, onRemoveItem, onEditItem }) => {
  const [priceComparison, setPriceComparison] = useState<PriceComparison | null>(null);
  const [isLoadingPrices, setIsLoadingPrices] = useState(false);
  const [showPriceDetails, setShowPriceDetails] = useState(false);

  // Fetch price comparison when component mounts or productId changes
  useEffect(() => {
    if (item.productId) {
      setIsLoadingPrices(true);
      priceComparisonService.getOptimizedPriceComparison(item.productId)
        .then(comparison => {
          setPriceComparison(comparison);
        })
        .catch(error => {
          console.error('Error fetching price comparison:', error);
        })
        .finally(() => {
          setIsLoadingPrices(false);
        });
    }
  }, [item.productId]);

  // Calculate best price and total cost
  const cheapestOption = priceComparison ? priceComparisonService.getCheapestOption(priceComparison) : null;
  const bestPrice = cheapestOption ? cheapestOption.price * item.quantity : null;
  const savings = priceComparison ? priceComparisonService.calculateSavings(priceComparison, item.quantity) : 0;

  // Use real price data if available, fallback to estimated price
  const displayPrice = bestPrice || (item.pricePerUnit && item.quantity ? item.pricePerUnit * item.quantity : null);

  return (
    <div className={`border-b border-gray-200 dark:border-gray-700 ${item.isChecked ? 'opacity-60' : ''}`}>
      {/* Main item row */}
      <div className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-neutral-dark transition-colors">
        <button onClick={() => onToggleChecked(item.id)} className="mr-3 focus:outline-none" aria-label={item.isChecked ? 'Mark as uncompleted' : 'Mark as completed'}>
          {item.isChecked ? (
            <CheckCircleIcon className="h-7 w-7 text-green-500" />
          ) : (
            <CircleIcon className="h-7 w-7 text-gray-400 dark:text-gray-500 hover:text-primary" />
          )}
        </button>

        <div className="flex-grow">
          <p className={`font-medium text-lg ${item.isChecked ? 'line-through text-gray-500 dark:text-gray-400' : 'text-gray-800 dark:text-gray-100'}`}>
            {item.name}
          </p>
          <div className="text-xs text-gray-500 dark:text-gray-400 space-x-2">
            {item.brand && <span>Brand: {item.brand}</span>}
            <span>Qty: {item.quantity}{item.unit ? ` ${item.unit}` : ''}</span>
            {isLoadingPrices && <span>Loading prices...</span>}
            {cheapestOption && !isLoadingPrices && (
              <span className="text-green-600 dark:text-green-400">
                Best: {cheapestOption.supermarketName} @ ${cheapestOption.price.toFixed(2)}
                {savings > 0 && <span className="ml-1">(Save ${savings.toFixed(2)})</span>}
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 ml-auto">
          {displayPrice && (
            <div className="text-right">
              <span className={`font-semibold text-sm min-w-[50px] ${item.isChecked ? 'text-gray-400 dark:text-gray-500' : 'text-primary'}`}>
                ${displayPrice.toFixed(2)}
              </span>
              {priceComparison && (
                <button
                  onClick={() => setShowPriceDetails(!showPriceDetails)}
                  className="block text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  {showPriceDetails ? 'Hide' : 'Compare'} prices
                </button>
              )}
            </div>
          )}
          <button onClick={() => {alert('Edit item functionality coming soon!'); onEditItem(item)}} className="p-2 text-gray-400 hover:text-blue-500 dark:hover:text-blue-400" aria-label="Edit item">
            <PencilIcon className="h-5 w-5" />
          </button>
          <button onClick={() => onRemoveItem(item.id)} className="p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400" aria-label="Remove item">
            <TrashIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Price comparison details */}
      {showPriceDetails && priceComparison && (
        <div className="px-3 pb-3 bg-gray-50 dark:bg-neutral-darker">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price Comparison:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {priceComparison.prices.map(price => (
              <div
                key={price.supermarketId}
                className={`p-2 rounded border ${
                  price.supermarketId === cheapestOption?.supermarketId
                    ? 'bg-green-100 border-green-300 dark:bg-green-900 dark:border-green-700'
                    : 'bg-white border-gray-200 dark:bg-neutral-dark dark:border-gray-600'
                } ${!price.isAvailable ? 'opacity-50' : ''}`}
              >
                <div className="font-medium">{price.supermarketName}</div>
                <div className={price.isAvailable ? 'text-green-600 dark:text-green-400' : 'text-red-500 dark:text-red-400'}>
                  {price.isAvailable ? `$${(price.price * item.quantity).toFixed(2)}` : 'Not available'}
                </div>
                {price.isAvailable && (
                  <div className="text-gray-500 dark:text-gray-400">
                    ${price.price.toFixed(2)} each
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ShoppingListItemRow;
    