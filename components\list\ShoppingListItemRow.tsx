
import React from 'react';
import { ShoppingListItem } from '../../types';
import { TrashIcon, CheckCircleIcon, CircleIcon, PencilIcon } from '../../constants'; 

interface ShoppingListItemRowProps {
  item: ShoppingListItem;
  onToggleChecked: (itemId: string) => void;
  onRemoveItem: (itemId: string) => void;
  onEditItem: (item: ShoppingListItem) => void; // Placeholder for edit functionality
}

const ShoppingListItemRow: React.FC<ShoppingListItemRowProps> = ({ item, onToggleChecked, onRemoveItem, onEditItem }) => {
  const totalPrice = item.pricePerUnit && item.quantity ? (item.pricePerUnit * item.quantity).toFixed(2) : null;

  return (
    <div className={`flex items-center p-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-neutral-dark transition-colors ${item.isChecked ? 'opacity-60' : ''}`}>
      <button onClick={() => onToggleChecked(item.id)} className="mr-3 focus:outline-none" aria-label={item.isChecked ? 'Mark as uncompleted' : 'Mark as completed'}>
        {item.isChecked ? (
          <CheckCircleIcon className="h-7 w-7 text-green-500" />
        ) : (
          <CircleIcon className="h-7 w-7 text-gray-400 dark:text-gray-500 hover:text-primary" />
        )}
      </button>

      <div className="flex-grow">
        <p className={`font-medium text-lg ${item.isChecked ? 'line-through text-gray-500 dark:text-gray-400' : 'text-gray-800 dark:text-gray-100'}`}>
          {item.name}
        </p>
        <div className="text-xs text-gray-500 dark:text-gray-400 space-x-2">
          {item.brand && <span>Brand: {item.brand}</span>}
          <span>Qty: {item.quantity}{item.unit ? ` ${item.unit}` : ''}</span>
          {item.pricePerUnit && <span>@ ${item.pricePerUnit.toFixed(2)}/{item.unit || 'item'}</span>}
        </div>
      </div>

      <div className="flex items-center space-x-2 ml-auto">
        {totalPrice && (
          <span className={`font-semibold text-sm min-w-[50px] text-right ${item.isChecked ? 'text-gray-400 dark:text-gray-500' : 'text-primary'}`}>
            ${totalPrice}
          </span>
        )}
         <button onClick={() => {alert('Edit item functionality coming soon!'); onEditItem(item)}} className="p-2 text-gray-400 hover:text-blue-500 dark:hover:text-blue-400" aria-label="Edit item">
          <PencilIcon className="h-5 w-5" />
        </button>
        <button onClick={() => onRemoveItem(item.id)} className="p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400" aria-label="Remove item">
          <TrashIcon className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default ShoppingListItemRow;
    