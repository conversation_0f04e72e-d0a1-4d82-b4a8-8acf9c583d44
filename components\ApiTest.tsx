import React, { useState, useEffect } from 'react';
import { supermarketService } from '../services/supermarketService';
import { productCatalogService } from '../services/productCatalogService';

export const ApiTest: React.FC = () => {
  const [supermarkets, setSupermarkets] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testApi = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Testing API connections...');

        // Test supermarkets endpoint
        const supermarketsData = await supermarketService.getAllSupermarkets();
        console.log('Supermarkets data:', supermarketsData);
        setSupermarkets(supermarketsData);

        // Test products endpoint
        const productsData = await productCatalogService.getAllProducts();
        console.log('Products data:', productsData);
        setProducts(productsData);

        setLoading(false);
      } catch (err) {
        console.error('API Test Error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setLoading(false);
      }
    };

    testApi();
  }, []);

  if (loading) {
    return (
      <div style={{ padding: '20px', border: '2px solid #007bff', borderRadius: '8px', margin: '20px' }}>
        <h3>🧪 API Connection Test</h3>
        <p>Loading data from backend...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', border: '2px solid #dc3545', borderRadius: '8px', margin: '20px', backgroundColor: '#f8d7da' }}>
        <h3>❌ API Connection Failed</h3>
        <p><strong>Error:</strong> {error}</p>
        <p><strong>Troubleshooting:</strong></p>
        <ul>
          <li>Make sure backend server is running on port 5000</li>
          <li>Check browser console for CORS errors</li>
          <li>Verify proxy configuration in vite.config.ts</li>
        </ul>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', border: '2px solid #28a745', borderRadius: '8px', margin: '20px', backgroundColor: '#d4edda' }}>
      <h3>✅ API Connection Successful!</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>🏪 Supermarkets ({supermarkets.length})</h4>
        {supermarkets.map((supermarket) => (
          <div key={supermarket.id} style={{ 
            padding: '10px', 
            margin: '5px 0', 
            backgroundColor: supermarket.color || '#f0f0f0',
            color: 'white',
            borderRadius: '4px'
          }}>
            <strong>{supermarket.name}</strong> ({supermarket.id})
          </div>
        ))}
      </div>

      <div>
        <h4>📦 Products ({products.length})</h4>
        {products.slice(0, 5).map((product) => (
          <div key={product.id} style={{ 
            padding: '8px', 
            margin: '3px 0', 
            backgroundColor: '#f8f9fa',
            border: '1px solid #dee2e6',
            borderRadius: '4px'
          }}>
            <strong>{product.name}</strong> - {product.category} ({product.id})
          </div>
        ))}
        {products.length > 5 && (
          <p style={{ fontStyle: 'italic', color: '#666' }}>
            ... and {products.length - 5} more products
          </p>
        )}
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#fff3cd', border: '1px solid #ffeaa7', borderRadius: '4px' }}>
        <h4>🎯 Next Steps:</h4>
        <ul>
          <li>✅ Backend API is working</li>
          <li>✅ Frontend can fetch data</li>
          <li>✅ Proxy configuration is correct</li>
          <li>🔄 Your app components should now show real data!</li>
        </ul>
      </div>
    </div>
  );
};
