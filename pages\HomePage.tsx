
import React, { useState, useEffect, useCallback } from 'react';
import ShoppingListCard from '../components/list/ShoppingListCard';
import { ShoppingList } from '../types';
import { shoppingListService } from '../services/shoppingListService';
import Button from '../components/ui/Button';
import CreateListModal from '../components/list/CreateListModal';
import ShareListModal from '../components/list/ShareListModal'; // Import ShareListModal
import { PlusIcon } from '../constants';

const HomePage: React.FC = () => {
  const [lists, setLists] = useState<ShoppingList[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  
  // State for Share Modal
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [sharingListId, setSharingListId] = useState<string | null>(null);
  const [sharingListName, setSharingListName] = useState<string | null>(null);

  const fetchLists = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedLists = await shoppingListService.getAllLists();
      setLists(fetchedLists);
    } catch (error) {
      console.error("Failed to fetch lists:", error);
      // Handle error display to user if necessary
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLists();
  }, [fetchLists]);

  const handleCreateList = async (listData: Pick<ShoppingList, 'name' | 'themeColor'> & {iconName?: string}) => {
    try {
      await shoppingListService.createList(listData.name, listData.themeColor, listData.iconName);
      fetchLists(); // Re-fetch lists to include the new one
    } catch (error) {
      console.error("Failed to create list:", error);
      throw error; // Re-throw to be caught by modal
    }
  };
  
  const handleDeleteList = async (listId: string) => {
    if (window.confirm("Are you sure you want to delete this list? This action cannot be undone.")) {
        try {
            await shoppingListService.deleteList(listId);
            fetchLists(); // Refresh the list
        } catch (error) {
            console.error("Failed to delete list:", error);
            alert("Could not delete the list. Please try again.");
        }
    }
  };

  const handleOpenShareModal = (listId: string, listName: string) => {
    setSharingListId(listId);
    setSharingListName(listName);
    setIsShareModalOpen(true);
  };

  const handleCloseShareModal = () => {
    setIsShareModalOpen(false);
    setSharingListId(null);
    setSharingListName(null);
  };
  
  const handleCollaboratorAdded = () => {
    fetchLists(); // Re-fetch lists to show new collaborator
    // Optionally keep the share modal open or close it:
    // handleCloseShareModal(); 
  };


  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <svg className="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">My Lists</h1>
        <Button onClick={() => setIsCreateModalOpen(true)} leftIcon={<PlusIcon className="h-5 w-5" />}>
          New List
        </Button>
      </div>

      {lists.length === 0 && !isLoading && (
        <div className="text-center py-10">
          <img src="https://picsum.photos/seed/empty/300/200" alt="Empty state" className="mx-auto mb-4 rounded-lg opacity-70" />
          <p className="text-xl text-gray-600 dark:text-gray-400">No shopping lists yet.</p>
          <p className="text-gray-500 dark:text-gray-500">Click "New List" to get started!</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {lists.map((list) => (
          <ShoppingListCard 
            key={list.id} 
            list={list} 
            onDelete={handleDeleteList}
            onShare={handleOpenShareModal} 
          />
        ))}
      </div>

      <CreateListModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateList={handleCreateList}
      />
      <ShareListModal
        isOpen={isShareModalOpen}
        onClose={handleCloseShareModal}
        listId={sharingListId}
        listName={sharingListName}
        onCollaboratorAdded={handleCollaboratorAdded}
      />
    </div>
  );
};

export default HomePage;
