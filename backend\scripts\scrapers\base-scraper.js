const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class BaseScraper {
  constructor(supermarketId, baseUrl) {
    this.supermarketId = supermarketId;
    this.baseUrl = baseUrl;
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    
    // Set user agent to avoid blocking
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    // Set viewport
    await this.page.setViewport({ width: 1280, height: 720 });
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async scrapeProduct(productUrl, baseProductId) {
    try {
      console.log(`Scraping: ${productUrl}`);
      await this.page.goto(productUrl, { waitUntil: 'networkidle2', timeout: 30000 });
      
      // Wait a bit for dynamic content
      await this.page.waitForTimeout(2000);
      
      // Extract product data (override in subclasses)
      const productData = await this.extractProductData(baseProductId);
      
      return productData;
    } catch (error) {
      console.error(`Error scraping ${productUrl}:`, error.message);
      return null;
    }
  }

  // Override this method in subclasses
  async extractProductData(baseProductId) {
    throw new Error('extractProductData must be implemented in subclass');
  }

  generateProductId(baseProductId) {
    return `${this.supermarketId}_${baseProductId}_${Date.now().toString().slice(-6)}`;
  }

  async saveProducts(products) {
    const outputPath = path.join(__dirname, '..', '..', 'data', 'supermarkets', `${this.supermarketId}-products.js`);
    const constantName = `${this.supermarketId.toUpperCase()}_PRODUCTS`;
    
    const jsContent = `// ${this.supermarketId} product catalog with pricing
const ${constantName} = ${JSON.stringify(products, null, 2)};

module.exports = { ${constantName} };`;

    fs.writeFileSync(outputPath, jsContent);
    console.log(`✅ Saved ${products.length} products to ${outputPath}`);
  }

  // Utility method to clean price strings
  cleanPrice(priceText) {
    if (!priceText) return 0;
    const cleaned = priceText.replace(/[^0-9.]/g, '');
    return parseFloat(cleaned) || 0;
  }

  // Utility method to clean text
  cleanText(text) {
    return text ? text.trim().replace(/\s+/g, ' ') : '';
  }
}

module.exports = BaseScraper;
