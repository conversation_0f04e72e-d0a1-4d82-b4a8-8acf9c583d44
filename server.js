const express = require('express');
const { MongoClient, ObjectId } = require('mongodb');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

async function connectToDatabase() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    return client.db('smart_shopper_db');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Routes
app.get('/api/shopping-lists', async (req, res) => {
  try {
    const db = await connectToDatabase();
    const lists = await db.collection('shopping_lists').find({}).toArray();
    res.json(lists);
  } catch (error) {
    console.error('Error fetching lists:', error);
    res.status(500).json({ error: 'Failed to fetch shopping lists' });
  }
});

app.get('/api/shopping-lists/:listId', async (req, res) => {
  try {
    const db = await connectToDatabase();
    const list = await db.collection('shopping_lists').findOne({ 
      _id: new ObjectId(req.params.listId) 
    });
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }
    res.json(list);
  } catch (error) {
    console.error('Error fetching list:', error);
    res.status(500).json({ error: 'Failed to fetch shopping list' });
  }
});

app.post('/api/shopping-lists', async (req, res) => {
  try {
    const { name, themeColor, iconName } = req.body;
    const db = await connectToDatabase();
    const result = await db.collection('shopping_lists').insertOne({
      name,
      themeColor,
      iconName,
      items: [],
      collaborators: [],
      createdAt: Date.now()
    });
    const newList = await db.collection('shopping_lists').findOne({ _id: result.insertedId });
    res.status(201).json(newList);
  } catch (error) {
    console.error('Error creating list:', error);
    res.status(500).json({ error: 'Failed to create shopping list' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});