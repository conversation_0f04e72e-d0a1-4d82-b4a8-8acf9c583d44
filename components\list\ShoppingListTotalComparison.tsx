import React, { useState, useEffect } from 'react';
import { ShoppingListItem } from '../../types';
import { priceComparisonService } from '../../services/priceComparisonService';

interface SupermarketTotal {
  supermarketId: string;
  supermarketName: string;
  totalCost: number;
  availableItems: number;
  unavailableItems: number;
  savings?: number;
}

interface ShoppingListTotalComparisonProps {
  items: ShoppingListItem[];
}

const ShoppingListTotalComparison: React.FC<ShoppingListTotalComparisonProps> = ({ items }) => {
  const [supermarketTotals, setSupermarketTotals] = useState<SupermarketTotal[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [bestOption, setBestOption] = useState<SupermarketTotal | null>(null);

  // Calculate totals for each supermarket
  useEffect(() => {
    const calculateTotals = async () => {
      if (items.length === 0) {
        setSupermarketTotals([]);
        setBestOption(null);
        return;
      }

      setIsLoading(true);
      
      try {
        // Initialize supermarket totals
        const supermarkets = [
          { id: 'paknsave', name: "Pak'nSave", color: '#FFD700' },
          { id: 'newworld', name: 'New World', color: '#E31E24' },
          { id: 'woolworths', name: 'Woolworths', color: '#00A651' },
          { id: 'warehouse', name: 'The Warehouse', color: '#D50000' }
        ];

        const totals: SupermarketTotal[] = supermarkets.map(sm => ({
          supermarketId: sm.id,
          supermarketName: sm.name,
          totalCost: 0,
          availableItems: 0,
          unavailableItems: 0
        }));

        // Calculate totals for items with productId (real products)
        const itemsWithProducts = items.filter(item => item.productId);
        
        for (const item of itemsWithProducts) {
          const priceComparison = await priceComparisonService.getOptimizedPriceComparison(item.productId!);
          
          if (priceComparison) {
            priceComparison.prices.forEach(price => {
              const supermarketTotal = totals.find(t => t.supermarketId === price.supermarketId);
              if (supermarketTotal) {
                if (price.isAvailable) {
                  supermarketTotal.totalCost += price.price * item.quantity;
                  supermarketTotal.availableItems += 1;
                } else {
                  supermarketTotal.unavailableItems += 1;
                }
              }
            });
          } else {
            // Product not found in price comparison, mark as unavailable
            totals.forEach(total => {
              total.unavailableItems += 1;
            });
          }
        }

        // Add estimated prices for items without productId
        const itemsWithoutProducts = items.filter(item => !item.productId && item.pricePerUnit);
        itemsWithoutProducts.forEach(item => {
          const estimatedCost = item.pricePerUnit! * item.quantity;
          totals.forEach(total => {
            total.totalCost += estimatedCost;
            total.availableItems += 1;
          });
        });

        // Calculate savings compared to most expensive option
        const availableTotals = totals.filter(t => t.availableItems > 0);
        if (availableTotals.length > 0) {
          const maxCost = Math.max(...availableTotals.map(t => t.totalCost));
          availableTotals.forEach(total => {
            total.savings = maxCost - total.totalCost;
          });

          // Find best option (lowest total cost with most available items)
          const best = availableTotals.reduce((best, current) => {
            if (current.availableItems > best.availableItems) return current;
            if (current.availableItems === best.availableItems && current.totalCost < best.totalCost) return current;
            return best;
          });
          setBestOption(best);
        }

        setSupermarketTotals(totals);
      } catch (error) {
        console.error('Error calculating shopping list totals:', error);
      } finally {
        setIsLoading(false);
      }
    };

    calculateTotals();
  }, [items]);

  if (items.length === 0) {
    return null;
  }

  const totalItems = items.length;
  const itemsWithPrices = items.filter(item => item.productId || item.pricePerUnit).length;

  return (
    <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
          🛒 Where to Shop for Best Prices
        </h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          {isExpanded ? 'Hide Details' : 'Show Details'}
        </button>
      </div>

      {isLoading ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Calculating best prices...</p>
        </div>
      ) : (
        <>
          {/* Best Option Summary */}
          {bestOption && (
            <div className="mb-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg border border-green-300 dark:border-green-700">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-300">
                    🏆 Best Option: {bestOption.supermarketName}
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-400">
                    Total: ${bestOption.totalCost.toFixed(2)} • Save ${bestOption.savings?.toFixed(2) || '0.00'}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-xs text-green-600 dark:text-green-400">
                    {bestOption.availableItems}/{totalItems} items available
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Detailed Comparison */}
          {isExpanded && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-3">
                Price Comparison by Supermarket:
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {supermarketTotals
                  .filter(total => total.availableItems > 0)
                  .sort((a, b) => a.totalCost - b.totalCost)
                  .map((total, index) => (
                    <div
                      key={total.supermarketId}
                      className={`p-3 rounded-lg border ${
                        total.supermarketId === bestOption?.supermarketId
                          ? 'bg-green-100 border-green-300 dark:bg-green-900/30 dark:border-green-700'
                          : 'bg-white border-gray-200 dark:bg-neutral-dark dark:border-gray-600'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="font-medium text-gray-800 dark:text-gray-100">
                            {index === 0 && '🏆 '}
                            {total.supermarketName}
                          </h5>
                          <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                            ${total.totalCost.toFixed(2)}
                          </p>
                          {total.savings && total.savings > 0 && (
                            <p className="text-sm text-green-600 dark:text-green-400">
                              Save ${total.savings.toFixed(2)}
                            </p>
                          )}
                        </div>
                        <div className="text-right text-xs text-gray-500 dark:text-gray-400">
                          <div>{total.availableItems}/{totalItems} available</div>
                          {total.unavailableItems > 0 && (
                            <div className="text-red-500 dark:text-red-400">
                              {total.unavailableItems} unavailable
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>

              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  💡 <strong>Tip:</strong> Shopping at {bestOption?.supermarketName} for these items will save you ${bestOption?.savings?.toFixed(2) || '0.00'} compared to the most expensive option!
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  Prices based on {itemsWithPrices}/{totalItems} items with available pricing data.
                </p>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ShoppingListTotalComparison;
