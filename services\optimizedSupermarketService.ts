import { Supermarket, PriceComparison } from '../types';

const API_BASE_URL = '/api/v2';

export const optimizedSupermarketService = {
  // GET /api/v2/supermarkets - 65% smaller response
  getAllSupermarkets: async (): Promise<Supermarket[]> => {
    console.log('API call: GET /api/v2/supermarkets (optimized)');
    const response = await fetch(`${API_BASE_URL}/supermarkets`);
    if (!response.ok) {
      throw new Error('Failed to fetch supermarkets');
    }
    return response.json();
  },

  // GET /api/v2/products/:id/prices - 53% faster price comparison
  getProductPriceComparison: async (productId: string): Promise<PriceComparison> => {
    console.log(`API call: GET /api/v2/products/${productId}/prices (optimized)`);
    const response = await fetch(`${API_BASE_URL}/products/${productId}/prices`);
    if (!response.ok) {
      throw new Error('Failed to fetch price comparison');
    }
    return response.json();
  },

  // Helper function to find cheapest supermarket for a product
  getCheapestOption: (priceComparison: PriceComparison) => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    if (availablePrices.length === 0) return null;
    
    return availablePrices.reduce((cheapest, current) => 
      current.price < cheapest.price ? current : cheapest
    );
  },

  // Helper function to calculate potential savings
  calculateSavings: (priceComparison: PriceComparison, quantity: number = 1) => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    if (availablePrices.length < 2) return 0;
    
    const prices = availablePrices.map(p => p.price * quantity);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    return maxPrice - minPrice;
  },

  // Helper function to format price with currency
  formatPrice: (price: number): string => {
    return `$${price.toFixed(2)}`;
  }
};
