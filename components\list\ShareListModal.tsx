
import React, { useState, useEffect } from 'react';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import { ClipboardIcon, CheckIcon, UserPlusIcon } from '../../constants';
import { shoppingListService } from '../../services/shoppingListService';

interface ShareListModalProps {
  isOpen: boolean;
  onClose: () => void;
  listId: string | null;
  listName: string | null;
  onCollaboratorAdded: () => void; // Callback to refresh list data
}

const ShareListModal: React.FC<ShareListModalProps> = ({
  isOpen,
  onClose,
  listId,
  listName,
  onCollaboratorAdded
}) => {
  const [shareLink, setShareLink] = useState('');
  const [isCopied, setIsCopied] = useState(false);
  const [collaboratorName, setCollaboratorName] = useState('');
  const [isAddingCollaborator, setIsAddingCollaborator] = useState(false);

  useEffect(() => {
    if (isOpen && listId) {
      // Construct the share link based on the current URL structure
      // For HashRouter, it's important to include the '#'
      const link = `${window.location.origin}${window.location.pathname}#/list/${listId}`;
      setShareLink(link);
    }
    // Reset states when modal opens or closes
    setIsCopied(false);
    setCollaboratorName('');
  }, [isOpen, listId]);

  if (!isOpen || !listId || !listName) return null;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareLink);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy: ', err);
      alert('Failed to copy link to clipboard.');
    }
  };

  const handleAddCollaborator = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!collaboratorName.trim()) {
      alert("Collaborator name cannot be empty.");
      return;
    }
    setIsAddingCollaborator(true);
    try {
      await shoppingListService.addCollaboratorToList(listId, collaboratorName.trim());
      onCollaboratorAdded(); // Notify parent to refresh data
      setCollaboratorName(''); // Clear input
      // Optionally, close modal or show success message here
    } catch (error) {
      console.error("Failed to add collaborator", error);
      alert("Error adding collaborator. Please try again.");
    } finally {
      setIsAddingCollaborator(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Share "${listName}"`}
    >
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Shareable Link
          </label>
          <div className="flex space-x-2">
            <Input
              type="text"
              value={shareLink}
              readOnly
              className="text-sm flex-grow"
              aria-label="Shareable link"
            />
            <Button
              onClick={handleCopyLink}
              variant="ghost"
              leftIcon={isCopied ? <CheckIcon className="h-5 w-5 text-green-500" /> : <ClipboardIcon className="h-5 w-5" />}
              className="whitespace-nowrap"
            >
              {isCopied ? 'Copied!' : 'Copy'}
            </Button>
          </div>
        </div>

        <hr className="dark:border-gray-600"/>

        <div>
          <h4 className="text-md font-medium text-gray-800 dark:text-gray-100 mb-2">Add Collaborator (Mock)</h4>
          <form onSubmit={handleAddCollaborator} className="space-y-3">
            <Input
              label="Collaborator Name"
              id="collaboratorName"
              value={collaboratorName}
              onChange={(e) => setCollaboratorName(e.target.value)}
              placeholder="Enter name"
              icon={<UserPlusIcon/>}
            />
            <Button type="submit" variant="secondary" className="w-full" isLoading={isAddingCollaborator} disabled={isAddingCollaborator || !collaboratorName.trim()}>
              Add Collaborator
            </Button>
          </form>
        </div>
        
      </div>
       <div className="flex justify-end space-x-3 pt-6 mt-4 border-t border-gray-200 dark:border-gray-700">
          <Button type="button" variant="ghost" onClick={onClose}>
            Close
          </Button>
        </div>
    </Modal>
  );
};

export default ShareListModal;
