
import React from 'react';
import { SortOption } from '../../types';
import Select from '../ui/Select';
import { ArrowUpIcon, ArrowDownIcon, Bars3Icon } from '../../constants';

interface SortControlsProps {
  currentSort: SortOption;
  onSortChange: (sortOption: SortOption) => void;
}

const sortOptionsConfig: { value: SortOption; label: string; icon?: React.ReactNode }[] = [
  { value: SortOption.DATE_ADDED_NEWEST, label: 'Date Added (Newest)', icon: <ArrowDownIcon className="h-4 w-4 inline mr-1"/> },
  { value: SortOption.DATE_ADDED_OLDEST, label: 'Date Added (Oldest)', icon: <ArrowUpIcon className="h-4 w-4 inline mr-1"/> },
  { value: SortOption.NAME_ASC, label: 'Name (A-Z)', icon: <ArrowUpIcon className="h-4 w-4 inline mr-1"/> },
  { value: SortOption.NAME_DESC, label: 'Name (Z-A)', icon: <ArrowDownIcon className="h-4 w-4 inline mr-1"/> },
  { value: SortOption.CHECKED_FIRST, label: 'Completed First' },
  { value: SortOption.UNCHECKED_FIRST, label: 'Pending First' },
];

const SortControls: React.FC<SortControlsProps> = ({ currentSort, onSortChange }) => {
  const selectOptions = sortOptionsConfig.map(opt => ({ value: opt.value, label: opt.label }));

  return (
    <div className="mb-4 flex items-center">
       <Bars3Icon className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
      <Select
        id="sortOption"
        options={selectOptions}
        value={currentSort}
        onChange={(e) => onSortChange(e.target.value as SortOption)}
        className="w-full md:w-auto"
        aria-label="Sort items by"
      />
    </div>
  );
};

export default SortControls;
    