/**
 * Optimized MongoDB Schema for Smart Shopper
 * Designed for fast queries and minimal data transfer
 */

// Collection 1: Products (Base catalog)
const ProductSchema = {
  _id: "ObjectId", // MongoDB auto-generated
  id: "prod_milk_1l", // Short, consistent ID
  name: "Milk",
  category: "dairy",
  icon: "milk", // Short icon reference
  image: "milk.jpg", // Filename only, construct full URL in frontend
  description: "Fresh milk 1 litre",
  tags: ["dairy", "fresh", "1l"], // For search optimization
  created: "2024-01-01T00:00:00Z",
  updated: "2024-01-01T00:00:00Z"
};

// Collection 2: Supermarkets (Static reference data)
const SupermarketSchema = {
  _id: "ObjectId",
  id: "paknsave", // Short ID
  name: "Pak'nSave",
  shortName: "PNS", // For compact displays
  color: "#FFD700",
  logo: "paknsave.png", // Filename only
  website: "paknsave.co.nz",
  active: true
};

// Collection 3: Prices (Optimized for fast lookups)
const PriceSchema = {
  _id: "ObjectId",
  productId: "prod_milk_1l", // Reference to product
  supermarketId: "paknsave", // Reference to supermarket
  price: 2.89,
  unit: "each",
  size: "1L",
  available: true,
  url: "/shop/product/anchor-blue-milk-1l", // Relative URL
  lastUpdated: "2024-01-01T00:00:00Z",
  // Compound index on productId + supermarketId for fast lookups
};

// Collection 4: Price History (For trends and analytics)
const PriceHistorySchema = {
  _id: "ObjectId",
  productId: "prod_milk_1l",
  supermarketId: "paknsave",
  price: 2.89,
  date: "2024-01-01",
  // Index on productId + date for trend queries
};

// Collection 5: Shopping Lists (Optimized structure)
const ShoppingListSchema = {
  _id: "ObjectId",
  id: "list_123", // Short ID for URLs
  name: "Weekly Shopping",
  userId: "user_456", // For multi-user support
  theme: "blue",
  icon: "cart",
  items: [
    {
      id: "item_789",
      productId: "prod_milk_1l", // Reference only
      quantity: 2,
      checked: false,
      addedAt: "2024-01-01T00:00:00Z"
    }
  ],
  collaborators: ["user_789"], // Array of user IDs
  created: "2024-01-01T00:00:00Z",
  updated: "2024-01-01T00:00:00Z",
  active: true
};

// Indexes for optimal performance
const OptimizedIndexes = {
  products: [
    { id: 1 }, // Unique index
    { category: 1 },
    { tags: 1 },
    { name: "text", description: "text", tags: "text" } // Text search
  ],
  prices: [
    { productId: 1, supermarketId: 1 }, // Compound index for fast price lookups
    { supermarketId: 1, available: 1 }, // For supermarket product lists
    { lastUpdated: -1 }, // For recent price updates
    { productId: 1, price: 1 } // For price sorting
  ],
  priceHistory: [
    { productId: 1, date: -1 }, // For price trends
    { supermarketId: 1, date: -1 } // For supermarket price history
  ],
  shoppingLists: [
    { userId: 1, active: 1 }, // User's active lists
    { id: 1 }, // Unique index for short IDs
    { "items.productId": 1 } // For finding lists containing specific products
  ]
};

module.exports = {
  ProductSchema,
  SupermarketSchema,
  PriceSchema,
  PriceHistorySchema,
  ShoppingListSchema,
  OptimizedIndexes
};
