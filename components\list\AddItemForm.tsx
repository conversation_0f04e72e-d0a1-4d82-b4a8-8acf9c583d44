
import React, { useState, useEffect, useCallback } from 'react';
import { ShoppingListItem, Product, SelectedProductInfo } from '../../types';
import { productCatalogService } from '../../services/productCatalogService';
import Input from '../ui/Input';
// Select removed as brand selection is deferred
import Button from '../ui/Button';
import { PlusIcon, TagIcon } from '../../constants';

interface AddItemFormProps {
  onAddItem: (
    item: Omit<ShoppingListItem, 'id' | 'addedAt' | 'isChecked' | 'brand'>,
    selectedProductInfo?: SelectedProductInfo
  ) => Promise<ShoppingListItem | null>; // Return added item for potential immediate follow-up
  listId: string; 
}

const AddItemForm: React.FC<AddItemFormProps> = ({ onAddItem }) => {
  const [itemNameInput, setItemNameInput] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [suggestedProducts, setSuggestedProducts] = useState<Product[]>([]);
  const [quantity, setQuantity] = useState<number | string>(1);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const resetForm = () => {
    setItemNameInput('');
    setSelectedProduct(null);
    setSuggestedProducts([]);
    setQuantity(1);
  };

  useEffect(() => {
    if (!itemNameInput.trim() || selectedProduct) {
      setSuggestedProducts([]);
      return;
    }

    setIsSearching(true);
    const handler = setTimeout(async () => {
      const products = await productCatalogService.findProductsByName(itemNameInput);
      setSuggestedProducts(products);
      setIsSearching(false);
    }, 300);

    return () => {
      clearTimeout(handler);
      setIsSearching(false); 
    };
  }, [itemNameInput, selectedProduct]);

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setItemNameInput(product.name); 
    setSuggestedProducts([]); 
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!itemNameInput.trim() || (typeof quantity === 'string' ? parseInt(quantity) : quantity) <= 0) {
      alert("Item name and valid quantity are required.");
      return;
    }
    setIsLoading(true);
    const finalQuantity = typeof quantity === 'string' ? parseInt(quantity) : quantity;

    const newItemData: Omit<ShoppingListItem, 'id' | 'addedAt' | 'isChecked' | 'brand'> = {
      name: itemNameInput,
      productId: selectedProduct?.id,
      quantity: finalQuantity,
      unit: selectedProduct?.defaultUnit || undefined,
      pricePerUnit: selectedProduct?.estimatedPrice || undefined,
    };
    
    const productInfoForBrandSelection = selectedProduct 
      ? { id: selectedProduct.id, name: selectedProduct.name, brands: selectedProduct.brands }
      : undefined;

    try {
      await onAddItem(newItemData, productInfoForBrandSelection);
      resetForm();
    } catch (error) {
      console.error("Error adding item:", error);
      alert("Failed to add item. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="p-4 bg-gray-50 dark:bg-neutral-darker rounded-lg shadow mb-6 space-y-4">
      <div className="relative">
        <Input
          label="Item Name"
          placeholder="e.g., Milk, Coffee, Apples"
          value={itemNameInput}
          onChange={(e) => {
            setItemNameInput(e.target.value);
            setSelectedProduct(null); 
          }}
          icon={<TagIcon />}
          required
          aria-autocomplete="list"
          aria-controls="product-suggestions"
        />
        {isSearching && <p className="text-xs text-gray-500 dark:text-gray-400 mt-1" aria-live="polite">Searching...</p>}
        {suggestedProducts.length > 0 && (
          <ul 
            id="product-suggestions"
            role="listbox"
            className="absolute z-10 w-full bg-white dark:bg-neutral-dark border border-gray-300 dark:border-gray-600 rounded-md shadow-lg mt-1 max-h-60 overflow-auto"
          >
            {suggestedProducts.map(p => (
              <li
                key={p.id}
                role="option"
                tabIndex={0}
                onClick={() => handleProductSelect(p)}
                onKeyDown={(e) => e.key === 'Enter' && handleProductSelect(p)}
                className="px-3 py-2 hover:bg-primary-light/20 dark:hover:bg-primary-dark/20 cursor-pointer focus:bg-primary-light/20 dark:focus:bg-primary-dark/20 outline-none"
              >
                {p.name} <span className="text-xs text-gray-500 dark:text-gray-400">({p.category})</span>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Brand Select removed from here */}

      <div className="grid grid-cols-1 gap-4"> 
        <Input
          label="Quantity"
          type="number"
          value={quantity}
          onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
          min="1"
          required
        />
      </div>

      <Button type="submit" variant="primary" className="w-full" isLoading={isLoading} leftIcon={<PlusIcon className="h-5 w-5"/>}>
        Add Item
      </Button>
    </form>
  );
};

export default AddItemForm;