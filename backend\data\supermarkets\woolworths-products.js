// Woolworths product catalog with pricing
const WOOLWORTHS_PRODUCTS = [
  {
    id: 'ww_milk_1l_001',
    productId: 'prod_milk_1l',
    supermarketId: 'woolworths',
    name: 'Anchor Blue Milk 1L',
    brand: 'Anchor',
    price: 3.09,
    unit: 'each',
    size: '1L',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/anchor-blue-milk-1l',
    imageUrl: 'https://picsum.photos/seed/ww_milk/200/200'
  },
  {
    id: 'ww_bread_white_001',
    productId: 'prod_bread_white',
    supermarketId: 'woolworths',
    name: 'Tip Top White Sandwich Bread',
    brand: 'Tip Top',
    price: 2.19,
    unit: 'each',
    size: '700g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/tip-top-white-bread',
    imageUrl: 'https://picsum.photos/seed/ww_bread/200/200'
  },
  {
    id: 'ww_eggs_dozen_001',
    productId: 'prod_eggs_dozen',
    supermarketId: 'woolworths',
    name: 'Woodland Free Range Eggs Size 7',
    brand: 'Woodland',
    price: 6.79,
    unit: 'dozen',
    size: '12 pack',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/woodland-free-range-eggs',
    imageUrl: 'https://picsum.photos/seed/ww_eggs/200/200'
  },
  {
    id: 'ww_bananas_001',
    productId: 'prod_bananas',
    supermarketId: 'woolworths',
    name: 'Bananas',
    brand: null,
    price: 3.29,
    unit: 'kg',
    size: null,
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/bananas',
    imageUrl: 'https://picsum.photos/seed/ww_bananas/200/200'
  },
  {
    id: 'ww_chicken_breast_001',
    productId: 'prod_chicken_breast',
    supermarketId: 'woolworths',
    name: 'Fresh Chicken Breast Fillets',
    brand: 'Tegel',
    price: 13.49,
    unit: 'kg',
    size: null,
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/chicken-breast',
    imageUrl: 'https://picsum.photos/seed/ww_chicken/200/200'
  },
  {
    id: 'ww_rice_1kg_001',
    productId: 'prod_rice_1kg',
    supermarketId: 'woolworths',
    name: 'SunRice Long Grain White Rice',
    brand: 'SunRice',
    price: 3.79,
    unit: 'each',
    size: '1kg',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/sunrice-white-rice',
    imageUrl: 'https://picsum.photos/seed/ww_rice/200/200'
  },
  {
    id: 'ww_pasta_500g_001',
    productId: 'prod_pasta_500g',
    supermarketId: 'woolworths',
    name: 'Barilla Spaghetti',
    brand: 'Barilla',
    price: 2.39,
    unit: 'each',
    size: '500g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/barilla-spaghetti',
    imageUrl: 'https://picsum.photos/seed/ww_pasta/200/200'
  },
  {
    id: 'ww_tomatoes_001',
    productId: 'prod_tomatoes',
    supermarketId: 'woolworths',
    name: 'Tomatoes',
    brand: null,
    price: 5.29,
    unit: 'kg',
    size: null,
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/tomatoes',
    imageUrl: 'https://picsum.photos/seed/ww_tomatoes/200/200'
  },
  {
    id: 'ww_cheese_tasty_001',
    productId: 'prod_cheese_tasty',
    supermarketId: 'woolworths',
    name: 'Mainland Tasty Cheese Block',
    brand: 'Mainland',
    price: 8.29,
    unit: 'each',
    size: '500g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/mainland-tasty-cheese',
    imageUrl: 'https://picsum.photos/seed/ww_cheese/200/200'
  },
  {
    id: 'ww_olive_oil_001',
    productId: 'prod_olive_oil',
    supermarketId: 'woolworths',
    name: 'Olivado Extra Virgin Olive Oil',
    brand: 'Olivado',
    price: 9.19,
    unit: 'each',
    size: '500ml',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.woolworths.co.nz/shop/product/olivado-olive-oil',
    imageUrl: 'https://picsum.photos/seed/ww_oil/200/200'
  }
];

module.exports = { WOOLWORTHS_PRODUCTS };
