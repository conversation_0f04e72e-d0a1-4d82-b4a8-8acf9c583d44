// The Warehouse product catalog with pricing (limited grocery selection)
const WAREHOUSE_PRODUCTS = [
  {
    id: 'tw_milk_1l_001',
    productId: 'prod_milk_1l',
    supermarketId: 'warehouse',
    name: 'Anchor Blue Milk 1L',
    brand: 'Anchor',
    price: 3.29,
    unit: 'each',
    size: '1L',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/anchor-blue-milk-1l',
    imageUrl: 'https://picsum.photos/seed/tw_milk/200/200'
  },
  {
    id: 'tw_bread_white_001',
    productId: 'prod_bread_white',
    supermarketId: 'warehouse',
    name: 'Tip Top White Sandwich Bread',
    brand: 'Tip Top',
    price: 2.39,
    unit: 'each',
    size: '700g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/tip-top-white-bread',
    imageUrl: 'https://picsum.photos/seed/tw_bread/200/200'
  },
  {
    id: 'tw_eggs_dozen_001',
    productId: 'prod_eggs_dozen',
    supermarketId: 'warehouse',
    name: 'Free Range Eggs Size 7',
    brand: 'Value',
    price: 5.99,
    unit: 'dozen',
    size: '12 pack',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/free-range-eggs',
    imageUrl: 'https://picsum.photos/seed/tw_eggs/200/200'
  },
  {
    id: 'tw_bananas_001',
    productId: 'prod_bananas',
    supermarketId: 'warehouse',
    name: 'Bananas',
    brand: null,
    price: 2.79,
    unit: 'kg',
    size: null,
    isAvailable: false, // The Warehouse doesn't typically sell fresh produce
    lastUpdated: Date.now(),
    productUrl: null,
    imageUrl: 'https://picsum.photos/seed/tw_bananas/200/200'
  },
  {
    id: 'tw_chicken_breast_001',
    productId: 'prod_chicken_breast',
    supermarketId: 'warehouse',
    name: 'Frozen Chicken Breast',
    brand: 'Tegel',
    price: 11.99,
    unit: 'kg',
    size: '1kg pack',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/frozen-chicken-breast',
    imageUrl: 'https://picsum.photos/seed/tw_chicken/200/200'
  },
  {
    id: 'tw_rice_1kg_001',
    productId: 'prod_rice_1kg',
    supermarketId: 'warehouse',
    name: 'SunRice Long Grain White Rice',
    brand: 'SunRice',
    price: 3.29,
    unit: 'each',
    size: '1kg',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/sunrice-white-rice',
    imageUrl: 'https://picsum.photos/seed/tw_rice/200/200'
  },
  {
    id: 'tw_pasta_500g_001',
    productId: 'prod_pasta_500g',
    supermarketId: 'warehouse',
    name: 'Barilla Spaghetti',
    brand: 'Barilla',
    price: 2.09,
    unit: 'each',
    size: '500g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/barilla-spaghetti',
    imageUrl: 'https://picsum.photos/seed/tw_pasta/200/200'
  },
  {
    id: 'tw_tomatoes_001',
    productId: 'prod_tomatoes',
    supermarketId: 'warehouse',
    name: 'Canned Tomatoes',
    brand: 'Watties',
    price: 1.99,
    unit: 'each',
    size: '400g can',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/watties-canned-tomatoes',
    imageUrl: 'https://picsum.photos/seed/tw_tomatoes/200/200'
  },
  {
    id: 'tw_cheese_tasty_001',
    productId: 'prod_cheese_tasty',
    supermarketId: 'warehouse',
    name: 'Mainland Tasty Cheese Block',
    brand: 'Mainland',
    price: 7.49,
    unit: 'each',
    size: '500g',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/mainland-tasty-cheese',
    imageUrl: 'https://picsum.photos/seed/tw_cheese/200/200'
  },
  {
    id: 'tw_olive_oil_001',
    productId: 'prod_olive_oil',
    supermarketId: 'warehouse',
    name: 'Olivado Extra Virgin Olive Oil',
    brand: 'Olivado',
    price: 8.49,
    unit: 'each',
    size: '500ml',
    isAvailable: true,
    lastUpdated: Date.now(),
    productUrl: 'https://www.thewarehouse.co.nz/p/olivado-olive-oil',
    imageUrl: 'https://picsum.photos/seed/tw_oil/200/200'
  }
];

module.exports = { WAREHOUSE_PRODUCTS };
