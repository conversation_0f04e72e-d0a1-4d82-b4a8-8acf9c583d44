<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Shopper</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class', // Enable class-based dark mode
        theme: {
          extend: {
            colors: {
              primary: {
                light: '#60a5fa', // blue-400
                DEFAULT: '#3b82f6', // blue-500
                dark: '#2563eb',  // blue-600
              },
              secondary: {
                light: '#a78bfa', // violet-400
                DEFAULT: '#8b5cf6', // violet-500
                dark: '#7c3aed', // violet-600
              },
              neutral: {
                lightest: '#f9fafb', // gray-50
                lighter: '#f3f4f6', // gray-100
                light: '#e5e7eb',   // gray-200 (used for some text)
                DEFAULT: '#6b7280', // gray-500 (general text)
                dark: '#374151',    // gray-700 (dark mode text / borders)
                darker: '#1f2937',  // gray-800 (dark mode component bg)
                darkest: '#111827', // gray-900 (dark mode main bg)
              }
            }
          }
        }
      }
    </script>
    <style>
      /* For custom scrollbar styling, optional */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: transparent;
      }
      html.dark ::-webkit-scrollbar-thumb {
        background-color: #4b5563; /* gray-600 */
        border-radius: 4px;
      }
      html:not(.dark) ::-webkit-scrollbar-thumb {
        background-color: #9ca3af; /* gray-400 */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background-color: #3b82f6; /* primary */
      }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-neutral-lighter dark:bg-neutral-darkest text-neutral-dark dark:text-neutral-light font-sans antialiased transition-colors duration-300">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>
