#!/usr/bin/env node

/**
 * Price monitoring system for supermarket products
 * Runs scheduled updates and tracks price changes
 */

const cron = require('node-cron');
const DataImporter = require('./data-importer');
const { MongoClient } = require('mongodb');

class PriceMonitor {
  constructor() {
    this.importer = new DataImporter();
    this.isRunning = false;
    this.updateInterval = process.env.PRICE_UPDATE_INTERVAL || '0 */6 * * *'; // Every 6 hours
  }

  async start() {
    console.log('🚀 Starting price monitor...');
    await this.importer.connect();
    
    // Schedule regular updates
    cron.schedule(this.updateInterval, async () => {
      if (!this.isRunning) {
        await this.runUpdate();
      }
    });
    
    console.log(`⏰ Scheduled price updates: ${this.updateInterval}`);
    
    // Run initial update
    await this.runUpdate();
  }

  async runUpdate() {
    this.isRunning = true;
    console.log(`🔄 Starting price update cycle at ${new Date().toISOString()}`);
    
    const supermarkets = ['paknsave', 'newworld', 'woolworths', 'warehouse'];
    
    for (const supermarketId of supermarkets) {
      try {
        await this.updateSupermarketPrices(supermarketId);
        
        // Wait between supermarkets to be respectful
        await this.delay(5000);
      } catch (error) {
        console.error(`❌ Error updating ${supermarketId}:`, error.message);
      }
    }
    
    console.log('✅ Price update cycle completed');
    this.isRunning = false;
  }

  async updateSupermarketPrices(supermarketId) {
    console.log(`📊 Updating prices for ${supermarketId}`);
    
    // This would integrate with your chosen data source
    // For now, simulate price fluctuations
    const priceUpdates = await this.simulatePriceChanges(supermarketId);
    
    if (priceUpdates.length > 0) {
      await this.importer.updatePrices(supermarketId, priceUpdates);
      await this.logPriceChanges(supermarketId, priceUpdates);
    }
  }

  async simulatePriceChanges(supermarketId) {
    // In a real implementation, this would:
    // 1. Scrape the supermarket website
    // 2. Call their API
    // 3. Import from a data feed
    
    const baseProducts = [
      'prod_milk_1l',
      'prod_bread_white', 
      'prod_eggs_dozen',
      'prod_bananas',
      'prod_chicken_breast'
    ];
    
    const updates = [];
    
    baseProducts.forEach(productId => {
      // Simulate price changes (±5%)
      if (Math.random() < 0.3) { // 30% chance of price change
        const currentPrice = this.getCurrentPrice(supermarketId, productId);
        const change = (Math.random() - 0.5) * 0.1; // ±5%
        const newPrice = Math.round((currentPrice * (1 + change)) * 100) / 100;
        
        updates.push({
          productId,
          price: newPrice,
          isAvailable: Math.random() > 0.05 // 95% availability
        });
      }
    });
    
    return updates;
  }

  getCurrentPrice(supermarketId, productId) {
    // Get current price from existing data
    const basePrices = {
      paknsave: { prod_milk_1l: 2.89, prod_bread_white: 1.99, prod_eggs_dozen: 6.49 },
      newworld: { prod_milk_1l: 3.19, prod_bread_white: 2.29, prod_eggs_dozen: 6.99 },
      woolworths: { prod_milk_1l: 3.09, prod_bread_white: 2.19, prod_eggs_dozen: 6.79 },
      warehouse: { prod_milk_1l: 3.29, prod_bread_white: 2.39, prod_eggs_dozen: 5.99 }
    };
    
    return basePrices[supermarketId]?.[productId] || 5.00;
  }

  async logPriceChanges(supermarketId, updates) {
    console.log(`📈 Price changes for ${supermarketId}:`);
    updates.forEach(update => {
      console.log(`  ${update.productId}: $${update.price} (${update.isAvailable ? 'available' : 'unavailable'})`);
    });
    
    // Log to database if available
    if (this.importer.db) {
      const logEntry = {
        supermarketId,
        timestamp: new Date(),
        changes: updates,
        changeCount: updates.length
      };
      
      await this.importer.db.collection('price_history').insertOne(logEntry);
    }
  }

  async getPriceHistory(productId, days = 30) {
    if (!this.importer.db) return [];
    
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    const history = await this.importer.db.collection('price_history').find({
      timestamp: { $gte: cutoff },
      'changes.productId': productId
    }).toArray();
    
    return history;
  }

  async generatePriceReport() {
    console.log('📊 Generating price report...');
    
    const supermarkets = ['paknsave', 'newworld', 'woolworths', 'warehouse'];
    const report = {
      timestamp: new Date().toISOString(),
      supermarkets: {}
    };
    
    for (const supermarketId of supermarkets) {
      const filePath = require('path').join(__dirname, '..', 'data', 'supermarkets', `${supermarketId}-products.js`);
      
      try {
        const { [supermarketId.toUpperCase() + '_PRODUCTS']: products } = require(filePath);
        
        report.supermarkets[supermarketId] = {
          totalProducts: products.length,
          availableProducts: products.filter(p => p.isAvailable).length,
          averagePrice: products.reduce((sum, p) => sum + p.price, 0) / products.length,
          lastUpdated: Math.max(...products.map(p => p.lastUpdated))
        };
      } catch (error) {
        report.supermarkets[supermarketId] = { error: error.message };
      }
    }
    
    console.log('📋 Price Report:', JSON.stringify(report, null, 2));
    return report;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async stop() {
    console.log('🛑 Stopping price monitor...');
    await this.importer.disconnect();
  }
}

// CLI interface
async function main() {
  const monitor = new PriceMonitor();
  
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'start':
        await monitor.start();
        
        // Keep running
        process.on('SIGINT', async () => {
          console.log('\n🛑 Shutting down...');
          await monitor.stop();
          process.exit(0);
        });
        break;
        
      case 'update':
        await monitor.importer.connect();
        await monitor.runUpdate();
        await monitor.stop();
        break;
        
      case 'report':
        await monitor.importer.connect();
        await monitor.generatePriceReport();
        await monitor.stop();
        break;
        
      default:
        console.log('Available commands:');
        console.log('  start   - Start continuous price monitoring');
        console.log('  update  - Run one-time price update');
        console.log('  report  - Generate price report');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    await monitor.stop();
  }
}

if (require.main === module) {
  main();
}

module.exports = PriceMonitor;
