const express = require('express');
const { MongoClient, ObjectId } = require('mongodb');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
let client;
let db;

async function connectToDatabase() {
  try {
    if (!client) {
      console.log('Connecting to MongoDB...');
      console.log('MongoDB URI:', uri);
      client = new MongoClient(uri, {
        serverSelectionTimeoutMS: 5000, // 5 second timeout
        connectTimeoutMS: 5000,
      });
      await client.connect();
      console.log('✅ Connected to MongoDB successfully');
      db = client.db('smart_shopper_db');
    }
    return db;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    console.log('💡 Make sure MongoDB is running or check your connection string');
    console.log('💡 For local MongoDB: mongodb://localhost:27017');
    console.log('💡 For MongoDB Atlas: mongodb+srv://username:<EMAIL>/database');

    // Don't exit immediately, allow server to start without DB for development
    if (process.env.NODE_ENV === 'development') {
      console.log('⚠️  Starting server without database connection (development mode)');
      return null;
    }
    process.exit(1);
  }
}

// Error handling middleware
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Validation helpers
const validateObjectId = (id) => {
  try {
    return new ObjectId(id);
  } catch (error) {
    throw new Error('Invalid ID format');
  }
};

// Import supermarket and product data
const { SUPERMARKETS } = require('./data/supermarkets');
const { BASE_PRODUCTS } = require('./data/base-products');
const { PAKNSAVE_PRODUCTS } = require('./data/supermarkets/paknsave-products');
const { NEWWORLD_PRODUCTS } = require('./data/supermarkets/newworld-products');
const { WOOLWORTHS_PRODUCTS } = require('./data/supermarkets/woolworths-products');
const { WAREHOUSE_PRODUCTS } = require('./data/supermarkets/warehouse-products');

// Combine all supermarket products
const ALL_SUPERMARKET_PRODUCTS = [
  ...PAKNSAVE_PRODUCTS,
  ...NEWWORLD_PRODUCTS,
  ...WOOLWORTHS_PRODUCTS,
  ...WAREHOUSE_PRODUCTS
];

// Initialize all data in database
async function initializeData() {
  try {
    const database = await connectToDatabase();
    if (!database) {
      console.log('⚠️  Skipping data initialization - database not available');
      return;
    }

    // Initialize supermarkets
    const supermarketsCollection = database.collection('supermarkets');
    const existingSupermarkets = await supermarketsCollection.countDocuments();
    if (existingSupermarkets === 0) {
      await supermarketsCollection.insertMany(SUPERMARKETS);
      console.log('✅ Supermarkets initialized in database');
    }

    // Initialize base products
    const productsCollection = database.collection('products');
    const existingProducts = await productsCollection.countDocuments();
    if (existingProducts === 0) {
      await productsCollection.insertMany(BASE_PRODUCTS);
      console.log('✅ Base products initialized in database');
    }

    // Initialize supermarket products
    const supermarketProductsCollection = database.collection('supermarket_products');
    const existingSupermarketProducts = await supermarketProductsCollection.countDocuments();
    if (existingSupermarketProducts === 0) {
      await supermarketProductsCollection.insertMany(ALL_SUPERMARKET_PRODUCTS);
      console.log('✅ Supermarket products initialized in database');
    }

    if (existingSupermarkets > 0 && existingProducts > 0 && existingSupermarketProducts > 0) {
      console.log('📦 All data already exists in database');
    }
  } catch (error) {
    console.error('❌ Error initializing data:', error.message);
  }
}

// SHOPPING LISTS ROUTES

// GET /api/shopping-lists - Get all shopping lists
app.get('/api/shopping-lists', asyncHandler(async (req, res) => {
  const database = await connectToDatabase();
  if (!database) {
    return res.status(503).json({ error: 'Database not available' });
  }

  const lists = await database.collection('shopping_lists').find({}).toArray();

  // Transform MongoDB _id to id for frontend compatibility
  const transformedLists = lists.map(list => ({
    ...list,
    id: list._id.toString(),
    _id: undefined
  }));

  res.json(transformedLists);
}));

// GET /api/shopping-lists/:listId - Get specific shopping list
app.get('/api/shopping-lists/:listId', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const database = await connectToDatabase();
  
  const list = await database.collection('shopping_lists').findOne({ _id: listId });
  if (!list) {
    return res.status(404).json({ error: 'List not found' });
  }
  
  // Transform MongoDB _id to id for frontend compatibility
  const transformedList = {
    ...list,
    id: list._id.toString(),
    _id: undefined
  };
  
  res.json(transformedList);
}));

// POST /api/shopping-lists - Create new shopping list
app.post('/api/shopping-lists', asyncHandler(async (req, res) => {
  const { name, themeColor = 'blue', iconName } = req.body;
  
  if (!name || name.trim() === '') {
    return res.status(400).json({ error: 'List name is required' });
  }
  
  const database = await connectToDatabase();
  const newList = {
    name: name.trim(),
    themeColor,
    iconName,
    items: [],
    collaborators: [],
    createdAt: Date.now()
  };
  
  const result = await database.collection('shopping_lists').insertOne(newList);
  const createdList = await database.collection('shopping_lists').findOne({ _id: result.insertedId });
  
  // Transform MongoDB _id to id for frontend compatibility
  const transformedList = {
    ...createdList,
    id: createdList._id.toString(),
    _id: undefined
  };
  
  res.status(201).json(transformedList);
}));

// DELETE /api/shopping-lists/:listId - Delete shopping list
app.delete('/api/shopping-lists/:listId', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const database = await connectToDatabase();

  const result = await database.collection('shopping_lists').deleteOne({ _id: listId });
  if (result.deletedCount === 0) {
    return res.status(404).json({ error: 'List not found' });
  }

  res.status(204).send();
}));

// SHOPPING LIST ITEMS ROUTES

// POST /api/shopping-lists/:listId/items - Add item to shopping list
app.post('/api/shopping-lists/:listId/items', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const { name, productId, brand, quantity = 1, unit, pricePerUnit } = req.body;

  if (!name || name.trim() === '') {
    return res.status(400).json({ error: 'Item name is required' });
  }

  const database = await connectToDatabase();

  // Check if list exists
  const list = await database.collection('shopping_lists').findOne({ _id: listId });
  if (!list) {
    return res.status(404).json({ error: 'List not found' });
  }

  const newItem = {
    id: uuidv4(),
    name: name.trim(),
    productId,
    brand,
    quantity: Number(quantity) || 1,
    unit,
    pricePerUnit: pricePerUnit ? Number(pricePerUnit) : undefined,
    isChecked: false,
    addedAt: Date.now()
  };

  const result = await database.collection('shopping_lists').updateOne(
    { _id: listId },
    { $push: { items: newItem } }
  );

  if (result.modifiedCount === 0) {
    return res.status(500).json({ error: 'Failed to add item to list' });
  }

  res.status(201).json(newItem);
}));

// PUT /api/shopping-lists/:listId/items/:itemId - Update item in shopping list
app.put('/api/shopping-lists/:listId/items/:itemId', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const { itemId } = req.params;
  const updates = req.body;

  const database = await connectToDatabase();

  // Build update object for nested array item
  const updateFields = {};
  Object.keys(updates).forEach(key => {
    if (['name', 'productId', 'brand', 'quantity', 'unit', 'pricePerUnit', 'isChecked'].includes(key)) {
      updateFields[`items.$.${key}`] = updates[key];
    }
  });

  const result = await database.collection('shopping_lists').updateOne(
    { _id: listId, 'items.id': itemId },
    { $set: updateFields }
  );

  if (result.matchedCount === 0) {
    return res.status(404).json({ error: 'List or item not found' });
  }

  // Get the updated item
  const updatedList = await database.collection('shopping_lists').findOne({ _id: listId });
  const updatedItem = updatedList.items.find(item => item.id === itemId);

  res.json(updatedItem);
}));

// DELETE /api/shopping-lists/:listId/items/:itemId - Remove item from shopping list
app.delete('/api/shopping-lists/:listId/items/:itemId', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const { itemId } = req.params;

  const database = await connectToDatabase();

  const result = await database.collection('shopping_lists').updateOne(
    { _id: listId },
    { $pull: { items: { id: itemId } } }
  );

  if (result.matchedCount === 0) {
    return res.status(404).json({ error: 'List not found' });
  }

  if (result.modifiedCount === 0) {
    return res.status(404).json({ error: 'Item not found' });
  }

  res.status(204).send();
}));

// PATCH /api/shopping-lists/:listId/items/:itemId/toggle - Toggle item checked state
app.patch('/api/shopping-lists/:listId/items/:itemId/toggle', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const { itemId } = req.params;

  const database = await connectToDatabase();

  // First get the current item to toggle its state
  const list = await database.collection('shopping_lists').findOne({ _id: listId });
  if (!list) {
    return res.status(404).json({ error: 'List not found' });
  }

  const item = list.items.find(item => item.id === itemId);
  if (!item) {
    return res.status(404).json({ error: 'Item not found' });
  }

  const newCheckedState = !item.isChecked;

  const result = await database.collection('shopping_lists').updateOne(
    { _id: listId, 'items.id': itemId },
    { $set: { 'items.$.isChecked': newCheckedState } }
  );

  if (result.modifiedCount === 0) {
    return res.status(500).json({ error: 'Failed to toggle item state' });
  }

  // Return the updated item
  const updatedItem = { ...item, isChecked: newCheckedState };
  res.json(updatedItem);
}));

// COLLABORATORS ROUTES

// POST /api/shopping-lists/:listId/collaborators - Add collaborator to shopping list
app.post('/api/shopping-lists/:listId/collaborators', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.listId);
  const { name } = req.body;

  if (!name || name.trim() === '') {
    return res.status(400).json({ error: 'Collaborator name is required' });
  }

  const database = await connectToDatabase();

  // Check if list exists
  const list = await database.collection('shopping_lists').findOne({ _id: listId });
  if (!list) {
    return res.status(404).json({ error: 'List not found' });
  }

  const newCollaborator = {
    id: uuidv4(),
    name: name.trim(),
    avatarUrl: `https://ui-avatars.com/api/?name=${encodeURIComponent(name.trim())}&background=random`
  };

  const result = await database.collection('shopping_lists').updateOne(
    { _id: listId },
    { $push: { collaborators: newCollaborator } }
  );

  if (result.modifiedCount === 0) {
    return res.status(500).json({ error: 'Failed to add collaborator to list' });
  }

  // Return the updated list
  const updatedList = await database.collection('shopping_lists').findOne({ _id: listId });
  const transformedList = {
    ...updatedList,
    id: updatedList._id.toString(),
    _id: undefined
  };

  res.status(201).json(transformedList);
}));

// PRODUCTS ROUTES

// GET /api/products - Get all base products
app.get('/api/products', asyncHandler(async (req, res) => {
  const database = await connectToDatabase();
  if (!database) {
    // Return mock products when database is not available
    return res.json(BASE_PRODUCTS);
  }

  const products = await database.collection('products').find({}).toArray();
  res.json(products);
}));

// GET /api/products/search - Search products by name
app.get('/api/products/search', asyncHandler(async (req, res) => {
  const { q } = req.query;

  if (!q || q.trim() === '') {
    return res.json([]);
  }

  const database = await connectToDatabase();
  const searchTerm = q.trim().toLowerCase();

  if (!database) {
    // Use mock products when database is not available
    const results = BASE_PRODUCTS.filter(p =>
      p.name.toLowerCase().includes(searchTerm)
    );
    return res.json(results);
  }

  // Use MongoDB text search or regex for partial matching
  const products = await database.collection('products').find({
    name: { $regex: searchTerm, $options: 'i' }
  }).toArray();

  res.json(products);
}));

// GET /api/products/:id - Get product by ID
app.get('/api/products/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const database = await connectToDatabase();

  if (!database) {
    // Use mock products when database is not available
    const product = BASE_PRODUCTS.find(p => p.id === id);
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    return res.json(product);
  }

  const product = await database.collection('products').findOne({ id });
  if (!product) {
    return res.status(404).json({ error: 'Product not found' });
  }

  res.json(product);
}));

// SUPERMARKETS ROUTES

// GET /api/supermarkets - Get all supermarkets
app.get('/api/supermarkets', asyncHandler(async (req, res) => {
  const database = await connectToDatabase();
  if (!database) {
    return res.json(SUPERMARKETS);
  }

  const supermarkets = await database.collection('supermarkets').find({}).toArray();
  res.json(supermarkets);
}));

// GET /api/supermarkets/:id/products - Get products for specific supermarket
app.get('/api/supermarkets/:id/products', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const database = await connectToDatabase();

  if (!database) {
    const products = ALL_SUPERMARKET_PRODUCTS.filter(p => p.supermarketId === id);
    return res.json(products);
  }

  const products = await database.collection('supermarket_products').find({
    supermarketId: id
  }).toArray();
  res.json(products);
}));

// GET /api/products/:id/prices - Get price comparison across supermarkets for a product
app.get('/api/products/:id/prices', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const database = await connectToDatabase();

  let baseProduct;
  let supermarketProducts;

  if (!database) {
    baseProduct = BASE_PRODUCTS.find(p => p.id === id);
    supermarketProducts = ALL_SUPERMARKET_PRODUCTS.filter(p => p.productId === id);
  } else {
    baseProduct = await database.collection('products').findOne({ id });
    supermarketProducts = await database.collection('supermarket_products').find({
      productId: id
    }).toArray();
  }

  if (!baseProduct) {
    return res.status(404).json({ error: 'Product not found' });
  }

  const prices = supermarketProducts.map(sp => {
    const supermarket = SUPERMARKETS.find(s => s.id === sp.supermarketId);
    return {
      supermarketId: sp.supermarketId,
      supermarketName: supermarket?.name || sp.supermarketId,
      price: sp.price,
      unit: sp.unit,
      size: sp.size,
      isAvailable: sp.isAvailable,
      productUrl: sp.productUrl
    };
  });

  // Find lowest price
  const availablePrices = prices.filter(p => p.isAvailable);
  const lowestPrice = availablePrices.length > 0
    ? availablePrices.reduce((min, current) => current.price < min.price ? current : min)
    : null;

  const comparison = {
    productId: id,
    productName: baseProduct.name,
    prices,
    lowestPrice: lowestPrice ? {
      supermarketId: lowestPrice.supermarketId,
      price: lowestPrice.price
    } : null
  };

  res.json(comparison);
}));

// POST /api/shopping-lists/:id/price-comparison - Get total cost comparison for shopping list
app.post('/api/shopping-lists/:id/price-comparison', asyncHandler(async (req, res) => {
  const listId = validateObjectId(req.params.id);
  const database = await connectToDatabase();

  if (!database) {
    return res.status(503).json({ error: 'Database not available for price comparison' });
  }

  // Get the shopping list
  const list = await database.collection('shopping_lists').findOne({ _id: listId });
  if (!list) {
    return res.status(404).json({ error: 'Shopping list not found' });
  }

  const itemComparisons = [];
  const supermarketTotals = {};

  // Initialize supermarket totals
  SUPERMARKETS.forEach(supermarket => {
    supermarketTotals[supermarket.id] = {
      supermarketId: supermarket.id,
      supermarketName: supermarket.name,
      totalCost: 0,
      availableItems: 0,
      unavailableItems: 0
    };
  });

  // Process each item in the shopping list
  for (const item of list.items) {
    if (!item.productId) continue; // Skip items without product links

    // Get price comparison for this product
    const baseProduct = await database.collection('products').findOne({ id: item.productId });
    const supermarketProducts = await database.collection('supermarket_products').find({
      productId: item.productId
    }).toArray();

    if (!baseProduct) continue;

    const prices = supermarketProducts.map(sp => {
      const supermarket = SUPERMARKETS.find(s => s.id === sp.supermarketId);
      return {
        supermarketId: sp.supermarketId,
        supermarketName: supermarket?.name || sp.supermarketId,
        price: sp.price,
        unit: sp.unit,
        size: sp.size,
        isAvailable: sp.isAvailable,
        productUrl: sp.productUrl
      };
    });

    const availablePrices = prices.filter(p => p.isAvailable);
    const lowestPrice = availablePrices.length > 0
      ? availablePrices.reduce((min, current) => current.price < min.price ? current : min)
      : null;

    const priceComparison = {
      productId: item.productId,
      productName: baseProduct.name,
      prices,
      lowestPrice: lowestPrice ? {
        supermarketId: lowestPrice.supermarketId,
        price: lowestPrice.price
      } : null
    };

    itemComparisons.push({
      itemId: item.id,
      itemName: item.name,
      quantity: item.quantity,
      priceComparison
    });

    // Add to supermarket totals
    prices.forEach(price => {
      if (supermarketTotals[price.supermarketId]) {
        if (price.isAvailable) {
          supermarketTotals[price.supermarketId].totalCost += price.price * item.quantity;
          supermarketTotals[price.supermarketId].availableItems += 1;
        } else {
          supermarketTotals[price.supermarketId].unavailableItems += 1;
        }
      }
    });
  }

  const totalComparison = Object.values(supermarketTotals);

  // Find best overall option (lowest total cost with most available items)
  const availableOptions = totalComparison.filter(t => t.availableItems > 0);
  const bestOption = availableOptions.length > 0
    ? availableOptions.reduce((best, current) => {
        if (current.availableItems > best.availableItems) return current;
        if (current.availableItems === best.availableItems && current.totalCost < best.totalCost) return current;
        return best;
      })
    : null;

  const highestTotal = Math.max(...availableOptions.map(o => o.totalCost));
  const savings = bestOption ? highestTotal - bestOption.totalCost : 0;

  const result = {
    listId: req.params.id,
    totalComparison,
    itemComparisons,
    bestOverallOption: bestOption ? {
      supermarketId: bestOption.supermarketId,
      totalCost: bestOption.totalCost,
      savings: Math.max(0, savings)
    } : null
  };

  res.json(result);
}));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    database: db ? 'Connected' : 'Disconnected'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);

  if (err.message === 'Invalid ID format') {
    return res.status(400).json({ error: 'Invalid ID format' });
  }

  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  if (client) {
    await client.close();
    console.log('MongoDB connection closed');
  }
  process.exit(0);
});

// Start server
async function startServer() {
  try {
    await connectToDatabase();
    await initializeData();

    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
      console.log(`🏪 Supermarkets: http://localhost:${PORT}/api/supermarkets`);
      console.log(`📦 Products: http://localhost:${PORT}/api/products`);
      console.log(`💰 Price comparison example: http://localhost:${PORT}/api/products/prod_milk_1l/prices`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
