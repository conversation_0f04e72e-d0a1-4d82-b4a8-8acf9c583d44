import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

const Input: React.FC<InputProps> = ({ label, id, error, icon, className, ...props }) => {
  return (
    <div className="w-full">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
      )}
      <div className="relative rounded-md shadow-sm">
        {icon && React.isValidElement(icon) && (
          <div className="pointer-events-none absolute inset-y-0 left-0 pl-3 flex items-center">
             {React.cloneElement(
               icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, 
               { className: 'h-5 w-5 text-gray-400' }
             )}
          </div>
        )}
        <input
          id={id}
          className={`
            block w-full px-3 py-2 border rounded-md
            text-gray-900 dark:text-white 
            bg-white dark:bg-neutral-darker
            border-gray-300 dark:border-gray-600 
            focus:ring-primary focus:border-primary 
            disabled:opacity-50
            ${icon && React.isValidElement(icon) ? 'pl-10' : ''}
            ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            ${className}
          `}
          {...props}
        />
      </div>
      {error && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>}
    </div>
  );
};

export default Input;
