
import React, { useState } from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import HomePage from './pages/HomePage';
import ListPage from './pages/ListPage';
import Header from './components/layout/Header';
import SideNav from './components/layout/SideNav'; // Import SideNav
import { useTheme } from './contexts/ThemeContext';
import { ApiTest } from './components/ApiTest';

const App: React.FC = () => {
  const { theme } = useTheme();
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);

  const toggleSideNav = () => {
    setIsSideNavOpen(!isSideNavOpen);
  };

  return (
    <HashRouter>
      <div className={`flex flex-col min-h-screen ${theme}`}>
        <Header onToggleSideNav={toggleSideNav} /> {/* Pass toggle function */}
        <SideNav isOpen={isSideNavOpen} onClose={() => setIsSideNavOpen(false)} /> {/* Render SideNav */}
        <main className="flex-grow container mx-auto px-4 py-6">
          <ApiTest />
          <Routes>
            <Route path="/lists" element={<HomePage />} />
            <Route path="/list/:listId" element={<ListPage />} />
            <Route path="*" element={<Navigate to="/lists" replace />} />
          </Routes>
        </main>
        <footer className="text-center p-4 text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
          Smart Shopper © 2024
        </footer>
      </div>
    </HashRouter>
  );
};

export default App;
