const express = require('express');
const { MongoClient } = require('mongodb');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
let client;
let db;

async function connectToDatabase() {
  try {
    if (!client) {
      console.log('Connecting to MongoDB...');
      client = new MongoClient(uri, {
        serverSelectionTimeoutMS: 5000,
        connectTimeoutMS: 5000,
      });
      await client.connect();
      console.log('✅ Connected to MongoDB successfully');
      db = client.db('smart_shopper_db');
    }
    return db;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    console.log('⚠️  Starting server without database connection');
    return null;
  }
}

// Simple health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    database: db ? 'Connected' : 'Disconnected'
  });
});

// Get supermarkets
app.get('/api/supermarkets', async (req, res) => {
  try {
    const database = await connectToDatabase();
    if (!database) {
      return res.status(503).json({ error: 'Database not available' });
    }
    
    const supermarkets = await database.collection('supermarkets').find({}).toArray();
    res.json(supermarkets);
  } catch (error) {
    console.error('Error fetching supermarkets:', error);
    res.status(500).json({ error: 'Failed to fetch supermarkets' });
  }
});

// Get products
app.get('/api/products', async (req, res) => {
  try {
    const database = await connectToDatabase();
    if (!database) {
      return res.status(503).json({ error: 'Database not available' });
    }
    
    const products = await database.collection('products').find({}).toArray();
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Simple price comparison
app.get('/api/products/:id/prices', async (req, res) => {
  try {
    const { id } = req.params;
    const database = await connectToDatabase();
    
    if (!database) {
      return res.status(503).json({ error: 'Database not available' });
    }
    
    const product = await database.collection('products').findOne({ id });
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    const prices = await database.collection('supermarket_products').find({ productId: id }).toArray();
    const supermarkets = await database.collection('supermarkets').find({}).toArray();
    
    const priceComparison = prices.map(p => {
      const supermarket = supermarkets.find(s => s.id === p.supermarketId);
      return {
        supermarketId: p.supermarketId,
        supermarketName: supermarket?.name || p.supermarketId,
        price: p.price,
        unit: p.unit,
        size: p.size,
        isAvailable: p.isAvailable
      };
    });
    
    const availablePrices = priceComparison.filter(p => p.isAvailable);
    const lowestPrice = availablePrices.length > 0 
      ? availablePrices.reduce((min, curr) => curr.price < min.price ? curr : min)
      : null;
    
    res.json({
      productId: id,
      productName: product.name,
      prices: priceComparison,
      lowestPrice: lowestPrice ? {
        supermarketId: lowestPrice.supermarketId,
        price: lowestPrice.price
      } : null
    });
  } catch (error) {
    console.error('Error fetching price comparison:', error);
    res.status(500).json({ error: 'Failed to fetch price comparison' });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
async function startServer() {
  try {
    await connectToDatabase();
    
    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
      console.log(`🏪 Supermarkets: http://localhost:${PORT}/api/supermarkets`);
      console.log(`💰 Price comparison: http://localhost:${PORT}/api/products/prod_milk_1l/prices`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
