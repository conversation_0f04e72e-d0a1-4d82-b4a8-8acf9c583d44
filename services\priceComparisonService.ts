import { PriceComparison, SupermarketPrice } from '../types';

const API_BASE_URL = '/api';

export const priceComparisonService = {
  // GET /api/products/:id/prices - Get price comparison for a product
  getProductPriceComparison: async (productId: string): Promise<PriceComparison | null> => {
    console.log(`API call: GET /api/products/${productId}/prices`);
    try {
      const response = await fetch(`${API_BASE_URL}/products/${productId}/prices`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch price comparison');
      }
      return response.json();
    } catch (error) {
      console.error('Error fetching price comparison:', error);
      return null;
    }
  },

  // GET /api/v2/products/:id/prices - Get optimized price comparison (65% smaller response)
  getOptimizedPriceComparison: async (productId: string): Promise<PriceComparison | null> => {
    console.log(`API call: GET /api/v2/products/${productId}/prices (optimized)`);
    try {
      const response = await fetch(`${API_BASE_URL}/v2/products/${productId}/prices`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch optimized price comparison');
      }
      return response.json();
    } catch (error) {
      console.error('Error fetching optimized price comparison:', error);
      // Fallback to regular API
      return this.getProductPriceComparison(productId);
    }
  },

  // Find the cheapest supermarket for a product
  getCheapestOption: (priceComparison: PriceComparison): SupermarketPrice | null => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    if (availablePrices.length === 0) return null;
    
    return availablePrices.reduce((cheapest, current) => 
      current.price < cheapest.price ? current : cheapest
    );
  },

  // Calculate potential savings by shopping at cheapest vs most expensive
  calculateSavings: (priceComparison: PriceComparison, quantity: number = 1): number => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    if (availablePrices.length < 2) return 0;
    
    const prices = availablePrices.map(p => p.price * quantity);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    
    return maxPrice - minPrice;
  },

  // Get price for a specific supermarket
  getPriceAtSupermarket: (priceComparison: PriceComparison, supermarketId: string): SupermarketPrice | null => {
    return priceComparison.prices.find(p => p.supermarketId === supermarketId) || null;
  },

  // Format price with currency
  formatPrice: (price: number): string => {
    return `$${price.toFixed(2)}`;
  },

  // Get price summary for display
  getPriceSummary: (priceComparison: PriceComparison) => {
    const availablePrices = priceComparison.prices.filter(p => p.isAvailable);
    const cheapest = this.getCheapestOption(priceComparison);
    const savings = this.calculateSavings(priceComparison);
    
    return {
      productName: priceComparison.productName,
      cheapestPrice: cheapest ? this.formatPrice(cheapest.price) : 'N/A',
      cheapestStore: cheapest?.supermarketName || 'N/A',
      maxSavings: this.formatPrice(savings),
      availableAt: availablePrices.length,
      totalStores: priceComparison.prices.length
    };
  },

  // Calculate total cost for shopping list at each supermarket
  calculateShoppingListTotals: async (items: Array<{productId: string, quantity: number}>) => {
    const supermarketTotals: Record<string, {
      supermarketId: string,
      supermarketName: string,
      totalCost: number,
      availableItems: number,
      unavailableItems: number
    }> = {};

    // Initialize supermarket totals
    const supermarkets = ['paknsave', 'newworld', 'woolworths', 'warehouse'];
    const supermarketNames = {
      'paknsave': 'Pak\'nSave',
      'newworld': 'New World', 
      'woolworths': 'Woolworths',
      'warehouse': 'The Warehouse'
    };

    supermarkets.forEach(id => {
      supermarketTotals[id] = {
        supermarketId: id,
        supermarketName: supermarketNames[id as keyof typeof supermarketNames],
        totalCost: 0,
        availableItems: 0,
        unavailableItems: 0
      };
    });

    // Fetch price comparisons for all items
    for (const item of items) {
      const priceComparison = await this.getOptimizedPriceComparison(item.productId);
      
      if (priceComparison) {
        priceComparison.prices.forEach(price => {
          if (supermarketTotals[price.supermarketId]) {
            if (price.isAvailable) {
              supermarketTotals[price.supermarketId].totalCost += price.price * item.quantity;
              supermarketTotals[price.supermarketId].availableItems += 1;
            } else {
              supermarketTotals[price.supermarketId].unavailableItems += 1;
            }
          }
        });
      } else {
        // Product not found, mark as unavailable in all stores
        supermarkets.forEach(id => {
          supermarketTotals[id].unavailableItems += 1;
        });
      }
    }

    return Object.values(supermarketTotals);
  }
};
