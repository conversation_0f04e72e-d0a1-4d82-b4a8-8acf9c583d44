
import React from 'react';
import { ICON_MAP as ActualIconMapConstant } from './constants';

export interface Collaborator {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface ShoppingListItem {
  id: string;
  name: string; // User-entered name or selected product name
  productId?: string; // Link to Product.id if selected from catalog
  brand?: string;
  quantity: number;
  unit?: string;
  pricePerUnit?: number; // Mocked price
  isChecked: boolean;
  addedAt: number; // Timestamp
}

export interface ShoppingList {
  id: string;
  name: string;
  items: ShoppingListItem[];
  collaborators: Collaborator[];
  createdAt: number; // Timestamp
  themeColor: string; // e.g., 'red', 'teal', 'blue'
  icon?: React.FC<React.SVGProps<SVGSVGElement>>; // Optional icon for the list
}

export interface Product {
  id: string;
  name: string; // e.g., "Milk", "Coffee", "Eggs"
  iconName?: keyof typeof ActualIconMapConstant; // Reference to an icon in ICON_MAP (now correctly typed)
  brands?: string[]; // e.g., ["Brand A", "Brand B"]
  defaultUnit?: string; // e.g., "liter", "pack", "dozen"
  category: string; // e.g., "Dairy", "Pantry", "Produce"
  estimatedPrice?: number; // Average price for catalog item
  imageUrl?: string; // URL for the product image
}

export interface SelectedProductInfo {
  id: string;
  name: string; // Keep name for display in modal title
  brands?: string[];
  imageUrl?: string; // Include imageUrl here if needed directly on selection
}


// Map of icon names to actual SVG components. To be defined in constants.tsx
export interface IconMap {
  [key: string]: React.FC<React.SVGProps<SVGSVGElement>>;
}

// Removed: export type ICON_MAP = IconMap; // This alias was causing issues with typeof

export enum SortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  DATE_ADDED_NEWEST = 'date_newest',
  DATE_ADDED_OLDEST = 'date_oldest',
  CHECKED_FIRST = 'checked_first',
  UNCHECKED_FIRST = 'unchecked_first',
}