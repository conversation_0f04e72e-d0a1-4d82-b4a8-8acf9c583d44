
import React from 'react';
import { ICON_MAP as ActualIconMapConstant } from './constants';

export interface Collaborator {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface ShoppingListItem {
  id: string;
  name: string; // User-entered name or selected product name
  productId?: string; // Link to Product.id if selected from catalog
  brand?: string;
  quantity: number;
  unit?: string;
  pricePerUnit?: number; // Mocked price
  isChecked: boolean;
  addedAt: number; // Timestamp
}

export interface ShoppingList {
  id: string;
  name: string;
  items: ShoppingListItem[];
  collaborators: Collaborator[];
  createdAt: number; // Timestamp
  themeColor: string; // e.g., 'red', 'teal', 'blue'
  icon?: React.FC<React.SVGProps<SVGSVGElement>>; // Optional icon for the list
}

export interface Supermarket {
  id: string;
  name: string; // e.g., "Pak'nSave", "New World", "Woolworths", "The Warehouse"
  logo?: string; // URL to supermarket logo
  color: string; // Brand color for UI
  website?: string;
}

export interface Product {
  id: string;
  name: string; // e.g., "Milk", "Coffee", "Eggs"
  iconName?: keyof typeof ActualIconMapConstant; // Reference to an icon in ICON_MAP (now correctly typed)
  category: string; // e.g., "Dairy", "Pantry", "Produce"
  imageUrl?: string; // URL for the product image
  description?: string; // Product description
}

export interface SupermarketProduct {
  id: string;
  productId: string; // Links to base Product
  supermarketId: string; // Links to Supermarket
  name: string; // Supermarket-specific product name
  brand?: string;
  price: number; // Current price at this supermarket
  unit: string; // e.g., "each", "kg", "litre", "pack"
  size?: string; // e.g., "1L", "500g", "6 pack"
  isAvailable: boolean;
  lastUpdated: number; // Timestamp of last price update
  productUrl?: string; // Link to product on supermarket website
  imageUrl?: string; // Supermarket-specific product image
}

export interface PriceComparison {
  productId: string;
  productName: string;
  prices: {
    supermarketId: string;
    supermarketName: string;
    price: number;
    unit: string;
    size?: string;
    isAvailable: boolean;
    productUrl?: string;
  }[];
  lowestPrice?: {
    supermarketId: string;
    price: number;
  };
}

export interface ShoppingListPriceComparison {
  listId: string;
  totalComparison: {
    supermarketId: string;
    supermarketName: string;
    totalCost: number;
    availableItems: number;
    unavailableItems: number;
  }[];
  itemComparisons: {
    itemId: string;
    itemName: string;
    quantity: number;
    priceComparison: PriceComparison;
  }[];
  bestOverallOption?: {
    supermarketId: string;
    totalCost: number;
    savings: number;
  };
}

export interface SelectedProductInfo {
  id: string;
  name: string; // Keep name for display in modal title
  brands?: string[];
  imageUrl?: string; // Include imageUrl here if needed directly on selection
}


// Map of icon names to actual SVG components. To be defined in constants.tsx
export interface IconMap {
  [key: string]: React.FC<React.SVGProps<SVGSVGElement>>;
}

// Removed: export type ICON_MAP = IconMap; // This alias was causing issues with typeof

export enum SortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  DATE_ADDED_NEWEST = 'date_newest',
  DATE_ADDED_OLDEST = 'date_oldest',
  CHECKED_FIRST = 'checked_first',
  UNCHECKED_FIRST = 'unchecked_first',
}