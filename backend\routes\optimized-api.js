/**
 * Optimized API routes for better performance and smaller payloads
 * Use these instead of the current routes for better performance
 */

const express = require('express');
const router = express.Router();

// Middleware to handle database connection
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// GET /api/v2/supermarkets - Compact supermarket list
router.get('/supermarkets', asyncHandler(async (req, res) => {
  const db = req.app.locals.db; // Assuming db is attached to app
  
  // Use compact collection for smaller payload
  const supermarkets = await db.collection('supermarkets_compact').find({}).toArray();
  
  res.json(supermarkets);
}));

// GET /api/v2/products/search - Fast product search
router.get('/products/search', asyncHandler(async (req, res) => {
  const { q, limit = 20 } = req.query;
  const db = req.app.locals.db;
  
  if (!q || q.trim() === '') {
    return res.json([]);
  }
  
  // Use search-optimized collection with text index
  const products = await db.collection('products_search')
    .find(
      { $text: { $search: q.trim() } },
      { score: { $meta: 'textScore' } }
    )
    .sort({ score: { $meta: 'textScore' } })
    .limit(parseInt(limit))
    .toArray();
  
  res.json(products);
}));

// GET /api/v2/products/:id/prices - Ultra-fast price comparison
router.get('/products/:id/prices', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const db = req.app.locals.db;
  
  // Use pre-calculated price comparison cache
  const comparison = await db.collection('price_comparisons').findOne({ productId: id });
  
  if (!comparison) {
    return res.status(404).json({ error: 'Product not found' });
  }
  
  // Remove MongoDB _id and updated fields for cleaner response
  const { _id, updated, ...cleanComparison } = comparison;
  
  res.json(cleanComparison);
}));

// GET /api/v2/supermarkets/:id/products - Optimized supermarket products
router.get('/supermarkets/:id/products', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { limit = 50, offset = 0 } = req.query;
  const db = req.app.locals.db;
  
  // Use optimized prices collection with aggregation
  const products = await db.collection('prices').aggregate([
    { $match: { supermarketId: id, available: true } },
    { $lookup: {
        from: 'products_search',
        localField: 'productId',
        foreignField: 'id',
        as: 'product'
      }
    },
    { $unwind: '$product' },
    { $project: {
        productId: 1,
        name: '$product.name',
        category: '$product.category',
        icon: '$product.icon',
        price: 1,
        unit: 1,
        size: 1
      }
    },
    { $sort: { 'product.name': 1 } },
    { $skip: parseInt(offset) },
    { $limit: parseInt(limit) }
  ]).toArray();
  
  res.json(products);
}));

// POST /api/v2/shopping-lists/:id/price-comparison - Optimized list comparison
router.post('/shopping-lists/:id/price-comparison', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const db = req.app.locals.db;
  
  // Get shopping list
  const list = await db.collection('shopping_lists').findOne({ 
    $or: [{ _id: new ObjectId(id) }, { id }] 
  });
  
  if (!list) {
    return res.status(404).json({ error: 'Shopping list not found' });
  }
  
  // Get all product IDs from the list
  const productIds = list.items.map(item => item.productId).filter(Boolean);
  
  if (productIds.length === 0) {
    return res.json({
      listId: id,
      totalComparison: [],
      itemComparisons: [],
      bestOverallOption: null
    });
  }
  
  // Use optimized prices collection for fast lookup
  const allPrices = await db.collection('prices').find({
    productId: { $in: productIds },
    available: true
  }).toArray();
  
  // Get supermarket info
  const supermarkets = await db.collection('supermarkets_compact').find({}).toArray();
  
  // Calculate totals per supermarket
  const supermarketTotals = {};
  supermarkets.forEach(sm => {
    supermarketTotals[sm.id] = {
      supermarketId: sm.id,
      supermarketName: sm.name,
      totalCost: 0,
      availableItems: 0,
      unavailableItems: 0
    };
  });
  
  // Process each item in the shopping list
  const itemComparisons = [];
  
  for (const item of list.items) {
    if (!item.productId) continue;
    
    const itemPrices = allPrices.filter(p => p.productId === item.productId);
    const quantity = item.quantity || 1;
    
    if (itemPrices.length > 0) {
      // Add to supermarket totals
      itemPrices.forEach(price => {
        if (supermarketTotals[price.supermarketId]) {
          supermarketTotals[price.supermarketId].totalCost += price.price * quantity;
          supermarketTotals[price.supermarketId].availableItems += 1;
        }
      });
      
      // Create item comparison
      const prices = itemPrices.map(p => {
        const supermarket = supermarkets.find(s => s.id === p.supermarketId);
        return {
          supermarketId: p.supermarketId,
          supermarketName: supermarket?.name || p.supermarketId,
          price: p.price,
          unit: p.unit,
          size: p.size,
          available: p.available
        };
      });
      
      const lowestPrice = prices.reduce((min, curr) => 
        curr.price < min.price ? curr : min
      );
      
      itemComparisons.push({
        itemId: item.id,
        itemName: item.name,
        quantity,
        priceComparison: {
          productId: item.productId,
          productName: item.name,
          prices,
          lowestPrice: {
            supermarketId: lowestPrice.supermarketId,
            price: lowestPrice.price
          }
        }
      });
    } else {
      // Mark as unavailable in all supermarkets
      supermarkets.forEach(sm => {
        supermarketTotals[sm.id].unavailableItems += 1;
      });
    }
  }
  
  const totalComparison = Object.values(supermarketTotals);
  
  // Find best overall option
  const availableOptions = totalComparison.filter(t => t.availableItems > 0);
  const bestOption = availableOptions.length > 0 
    ? availableOptions.reduce((best, current) => {
        if (current.availableItems > best.availableItems) return current;
        if (current.availableItems === best.availableItems && current.totalCost < best.totalCost) return current;
        return best;
      })
    : null;
  
  const highestTotal = Math.max(...availableOptions.map(o => o.totalCost));
  const savings = bestOption ? highestTotal - bestOption.totalCost : 0;
  
  res.json({
    listId: id,
    totalComparison,
    itemComparisons,
    bestOverallOption: bestOption ? {
      supermarketId: bestOption.supermarketId,
      totalCost: bestOption.totalCost,
      savings: Math.max(0, savings)
    } : null
  });
}));

// GET /api/v2/stats - Quick stats for dashboard
router.get('/stats', asyncHandler(async (req, res) => {
  const db = req.app.locals.db;
  
  const [
    productCount,
    supermarketCount,
    priceCount,
    listCount
  ] = await Promise.all([
    db.collection('products').countDocuments(),
    db.collection('supermarkets').countDocuments(),
    db.collection('prices').countDocuments({ available: true }),
    db.collection('shopping_lists').countDocuments()
  ]);
  
  res.json({
    products: productCount,
    supermarkets: supermarketCount,
    prices: priceCount,
    lists: listCount,
    lastUpdated: new Date()
  });
}));

module.exports = router;
