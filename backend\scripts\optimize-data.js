#!/usr/bin/env node

/**
 * Data optimization script
 * Migrates current data to optimized structure
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

class DataOptimizer {
  constructor() {
    this.mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    this.dbName = 'smart_shopper_db';
    this.client = null;
    this.db = null;
  }

  async connect() {
    this.client = new MongoClient(this.mongoUri);
    await this.client.connect();
    this.db = this.client.db(this.dbName);
    console.log('✅ Connected to MongoDB');
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
    }
  }

  // Option 1: Create optimized normalized structure
  async createOptimizedStructure() {
    console.log('🔄 Creating optimized normalized structure...');

    // Create optimized prices collection
    const prices = await this.db.collection('supermarket_products').find({}).toArray();
    const optimizedPrices = prices.map(p => ({
      productId: p.productId,
      supermarketId: p.supermarketId,
      price: p.price,
      unit: p.unit,
      size: p.size,
      available: p.isAvailable,
      url: p.productUrl ? p.productUrl.replace(/https?:\/\/[^\/]+/, '') : null, // Make relative
      lastUpdated: new Date(p.lastUpdated)
    }));

    // Drop old collection and create new optimized one
    await this.db.collection('prices_optimized').drop().catch(() => {});
    await this.db.collection('prices_optimized').insertMany(optimizedPrices);

    // Create indexes for fast queries
    await this.db.collection('prices_optimized').createIndexes([
      { key: { productId: 1, supermarketId: 1 }, unique: true },
      { key: { supermarketId: 1, available: 1 } },
      { key: { productId: 1, price: 1 } },
      { key: { lastUpdated: -1 } }
    ]);

    console.log(`✅ Created optimized prices collection with ${optimizedPrices.length} records`);
  }

  // Option 2: Create denormalized structure for ultra-fast reads
  async createDenormalizedStructure() {
    console.log('🔄 Creating denormalized structure...');

    const products = await this.db.collection('products').find({}).toArray();
    const prices = await this.db.collection('supermarket_products').find({}).toArray();

    const denormalizedProducts = products.map(product => {
      const productPrices = prices.filter(p => p.productId === product.id);
      
      const pricesByStore = {};
      let lowestPrice = { price: Infinity, supermarket: null };

      productPrices.forEach(p => {
        pricesByStore[p.supermarketId] = {
          price: p.price,
          unit: p.unit,
          size: p.size,
          available: p.isAvailable,
          url: p.productUrl ? p.productUrl.replace(/https?:\/\/[^\/]+/, '') : null,
          updated: new Date(p.lastUpdated)
        };

        if (p.isAvailable && p.price < lowestPrice.price) {
          lowestPrice = { price: p.price, supermarket: p.supermarketId };
        }
      });

      const allPrices = Object.values(pricesByStore).filter(p => p.available).map(p => p.price);
      const priceRange = allPrices.length > 0 ? {
        min: Math.min(...allPrices),
        max: Math.max(...allPrices),
        savings: Math.max(...allPrices) - Math.min(...allPrices)
      } : null;

      return {
        id: product.id,
        name: product.name,
        category: product.category,
        icon: product.iconName,
        image: product.imageUrl ? product.imageUrl.split('/').pop() : null, // Just filename
        description: product.description,
        prices: pricesByStore,
        lowestPrice: lowestPrice.supermarket ? lowestPrice : null,
        priceRange,
        lastUpdated: new Date()
      };
    });

    await this.db.collection('products_denormalized').drop().catch(() => {});
    await this.db.collection('products_denormalized').insertMany(denormalizedProducts);

    // Create indexes
    await this.db.collection('products_denormalized').createIndexes([
      { key: { id: 1 }, unique: true },
      { key: { category: 1 } },
      { key: { 'lowestPrice.price': 1 } },
      { key: { name: 'text', description: 'text' } }
    ]);

    console.log(`✅ Created denormalized products collection with ${denormalizedProducts.length} records`);
  }

  // Create compact API responses
  async createCompactViews() {
    console.log('🔄 Creating compact views...');

    // Compact supermarket list (for dropdowns, etc.)
    const supermarkets = await this.db.collection('supermarkets').find({}).toArray();
    const compactSupermarkets = supermarkets.map(s => ({
      id: s.id,
      name: s.name,
      short: s.name.split(' ')[0], // "Pak'nSave" -> "Pak'nSave", "New World" -> "New"
      color: s.color
    }));

    await this.db.collection('supermarkets_compact').drop().catch(() => {});
    await this.db.collection('supermarkets_compact').insertMany(compactSupermarkets);

    // Compact product list (for search results)
    const products = await this.db.collection('products').find({}).toArray();
    const compactProducts = products.map(p => ({
      id: p.id,
      name: p.name,
      category: p.category,
      icon: p.iconName
    }));

    await this.db.collection('products_compact').drop().catch(() => {});
    await this.db.collection('products_compact').insertMany(compactProducts);

    console.log('✅ Created compact views');
  }

  // Analyze current data size and performance
  async analyzeCurrentData() {
    console.log('📊 Analyzing current data structure...');

    const collections = ['supermarkets', 'products', 'supermarket_products', 'shopping_lists'];
    
    for (const collName of collections) {
      const stats = await this.db.collection(collName).stats();
      const count = await this.db.collection(collName).countDocuments();
      
      console.log(`\n📋 ${collName}:`);
      console.log(`   Documents: ${count}`);
      console.log(`   Storage Size: ${(stats.storageSize / 1024).toFixed(2)} KB`);
      console.log(`   Average Doc Size: ${(stats.avgObjSize || 0).toFixed(2)} bytes`);
      console.log(`   Indexes: ${stats.nindexes}`);
    }

    // Sample query performance test
    console.log('\n⚡ Performance Test:');
    
    const start = Date.now();
    await this.db.collection('supermarket_products').find({ productId: 'prod_milk_1l' }).toArray();
    const queryTime = Date.now() - start;
    
    console.log(`   Price lookup query: ${queryTime}ms`);
  }

  // Create optimized API endpoints data
  async createAPIOptimizedData() {
    console.log('🔄 Creating API-optimized data...');

    // Pre-calculate popular price comparisons
    const popularProducts = ['prod_milk_1l', 'prod_bread_white', 'prod_eggs_dozen'];
    const priceComparisons = {};

    for (const productId of popularProducts) {
      const product = await this.db.collection('products').findOne({ id: productId });
      const prices = await this.db.collection('supermarket_products').find({ productId }).toArray();
      
      const comparison = {
        productId,
        productName: product.name,
        prices: prices.map(p => ({
          supermarketId: p.supermarketId,
          price: p.price,
          available: p.isAvailable
        })),
        lastUpdated: new Date()
      };

      priceComparisons[productId] = comparison;
    }

    await this.db.collection('price_comparisons_cache').drop().catch(() => {});
    await this.db.collection('price_comparisons_cache').insertMany(Object.values(priceComparisons));

    console.log('✅ Created API-optimized cache');
  }
}

// CLI interface
async function main() {
  const optimizer = new DataOptimizer();
  await optimizer.connect();

  const command = process.argv[2];

  try {
    switch (command) {
      case 'analyze':
        await optimizer.analyzeCurrentData();
        break;
        
      case 'optimize':
        await optimizer.createOptimizedStructure();
        await optimizer.createCompactViews();
        await optimizer.createAPIOptimizedData();
        break;
        
      case 'denormalize':
        await optimizer.createDenormalizedStructure();
        break;
        
      case 'all':
        await optimizer.analyzeCurrentData();
        await optimizer.createOptimizedStructure();
        await optimizer.createDenormalizedStructure();
        await optimizer.createCompactViews();
        await optimizer.createAPIOptimizedData();
        break;
        
      default:
        console.log('Available commands:');
        console.log('  analyze      - Analyze current data structure');
        console.log('  optimize     - Create optimized normalized structure');
        console.log('  denormalize  - Create denormalized structure');
        console.log('  all          - Run all optimizations');
        console.log('');
        console.log('Example: node optimize-data.js analyze');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await optimizer.disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = DataOptimizer;
