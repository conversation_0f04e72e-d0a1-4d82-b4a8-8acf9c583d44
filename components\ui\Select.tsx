
import React from 'react';
import { ChevronDownIcon } from '../../constants';


interface SelectOption {
  value: string | number;
  label: string;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  options: SelectOption[];
  error?: string;
  placeholderOption?: string;
}

const Select: React.FC<SelectProps> = ({ label, id, options, error, className, placeholderOption, ...props }) => {
  return (
    <div className="w-full">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
      )}
      <div className="relative">
        <select
          id={id}
          className={`
            block w-full pl-3 pr-10 py-2 border rounded-md appearance-none
            text-gray-900 dark:text-white 
            bg-white dark:bg-neutral-darker
            border-gray-300 dark:border-gray-600 
            focus:ring-primary focus:border-primary 
            disabled:opacity-50
            ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
            ${className}
          `}
          {...props}
        >
          {placeholderOption && <option value="">{placeholderOption}</option>}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
          <ChevronDownIcon className="h-5 w-5" />
        </div>
      </div>
      {error && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>}
    </div>
  );
};

export default Select;
    